import { useEffect } from 'react';
import { Plus } from '@nutui/icons-react-taro';
import { Button, Cell, InfiniteLoading, Skeleton, Tag } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';

import { workOrderPage } from '@/api/workOrder';
import TitleBar from '@/components/TitleBar';
import { ActiviStatus } from '@/enums';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight } from '@/utils';

import './index.scss';

interface StateType {
  list: API.WorkOrderPageResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo: API.WorkOrderPageResp;
}

const listHeight = calcAvailableHeight(0, 44) + 'px';

const WorkOrder = () => {
  const navHeight = useSafeStatusbar();

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: {},
    hasMore: true,
  });
  const pagination = useReactive({
    pageNum: 1,
    pageSize: 15,
  });

  const { run: getList, loading } = useRequest(
    () => workOrderPage({ pageNum: pagination.pageNum, pageSize: pagination.pageSize }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.data?.records && res?.data?.records?.length > 0) {
          const filterData = res.data.records.filter(
            (item: API.WorkOrderPageResp) => item.activiStatus === '0',
          );
          if (pagination.pageNum === 1) {
            state.list = filterData;
          } else {
            state.list = [...state.list, ...filterData];
          }
        } else {
          if (pagination.pageNum === 1) state.list = [];
          state.hasMore = false;
        }
      },
    },
  );

  useEffect(() => {
    getList();
  }, []);

  //从详情页返回时请求
  const backHandleLists = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };
  useEffect(() => {
    Taro.eventCenter.on('WORK_ORDER_REFRESH', backHandleLists);
    return () => {
      Taro.eventCenter.off('WORK_ORDER_REFRESH');
    };
  }, []);

  const loadMore = async () => {
    pagination.pageNum += 1;
    getList();
  };

  const refresh = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  return (
    <View className='work'>
      <TitleBar title='工单列表' />
      <Button
        className='work-add'
        type='primary'
        onClick={() => {
          Taro.navigateTo({ url: 'components/Detail/index' });
        }}
      >
        <Plus className='work-add_icon' size='50' />
      </Button>
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='work-list'
      >
        {loading && pagination.pageNum === 1 ? (
          <Skeleton rows={20} animated />
        ) : (
          <ul id='scrollDemo' className='work-ul'>
            <InfiniteLoading
              pullingText='松开刷新'
              loadingText='加载中'
              loadMoreText='没有更多了'
              pullRefresh
              target='scrollDemo'
              hasMore={state.hasMore}
              onLoadMore={loadMore}
              onRefresh={refresh}
            >
              {state.list.map((record: API.WorkOrderPageResp) => {
                const statusObj = ActiviStatus.map((item) => ({
                  title: item.key === '0' ? '草稿' : item.title,
                  key: item.key,
                  background: item.key === '0' ? '#FFB800' : item.background,
                })).find((i) => i.key === record.activiStatus);
                return (
                  <View
                    className='work-cell_each'
                    key={record.id}
                    onClick={() => {
                      state.currentInfo = record;
                      Taro.navigateTo({ url: `components/Detail/index?id=${record.id}` });
                    }}
                  >
                    <Cell.Group divider={false}>
                      <Cell
                        className='work-cell_top'
                        title={
                          <View className='work-cell_title'>
                            <View>{record.documentNumber || '暂无编号'}</View>
                          </View>
                        }
                        extra={
                          <Tag className='work-cell_tag' type='warning'>
                            {statusObj?.title || '草稿'}
                          </Tag>
                        }
                      />
                      <Cell>
                        <View className='work-cell-desc'>
                          <View className='work-cell_line'>
                            <View className='work-cell_line_left'>项目名称：</View>
                            <View className='work-cell_line_right'>
                              {record.projectName || '暂无数据'}
                            </View>
                          </View>
                          <View className='work-cell_line'>
                            <View className='work-cell_line_left'>工程师姓名：</View>
                            <View className='work-cell_line_right'>
                              {record.employeeName || '暂无数据'}
                            </View>
                          </View>
                        </View>
                      </Cell>
                    </Cell.Group>
                  </View>
                );
              })}
            </InfiniteLoading>
          </ul>
        )}
      </View>
    </View>
  );
};

export default WorkOrder;
