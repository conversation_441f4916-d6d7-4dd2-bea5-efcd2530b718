.detail {
  --nutui-form-item-error-line-color: var(--error-color);
  --nutui-form-item-required-color: var(--error-color);
  --nutui-form-item-error-message-color: var(--error-color);
  padding: 15px;
  --nutui-uploader-preview-tips-height: 0;

  &-container {
    width: 100%;

    &_form {
      width: 100%;
      &_cell {
        padding: 0;
      }
      &_clear {
        margin-right: 20px;
        color: #999;
      }
      &_footer {
        width: 100%;
        margin-top: 10px;
      }
    }
  }

  .detail-main_actions {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10;
    display: flex;
    align-items: flex-start;
    justify-content: space-around;

    width: 100%;
    padding: 10px 0;
    background: #fff;
    box-shadow: var(--nutui-cell-box-shadow, 0px 1px 7px 0px rgb(237, 238, 241));

    &_button {
      flex: 1;
      color: var(--primary-color);
      font-size: 14px;
    }
    &_divider {
      position: relative;
      top: 5px;
      width: 1px;
      height: 20px;
      background: #e5e5e5;
    }
  }
  &_file {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 10px auto;
    padding: 6px 10px;
    color: #52dbc7;
    border-radius: 5px;
  }
}
