import { useState } from 'react';
import { ArrowCircleUp, Comment, Success } from '@nutui/icons-react-taro';
import { Badge, Button, Grid, <PERSON>up, Tabbar, Tabs } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';

import IconFont from '@/components/IconFont';
import TitleBar from '@/components/TitleBar';
import { approvalList, ApprovalStatus } from '@/enums';
import { useSafeStatusbar } from '@/hooks';
import { useLoginStore } from '@/store';
import { calcAvailableHeight } from '@/utils';

import ApprovalList from './components/ApprovalList';

import './index.scss';

const listHeight = calcAvailableHeight(105, 44) + 'px';

const Approvals = () => {
  const approvalData = useLoginStore((state) => state.approvalData);
  const approvalCount = useLoginStore((state) => state.approvalCount);

  const navHeight = useSafeStatusbar();
  const [currenTabValue, setTabValue] = useState(ApprovalStatus[0]['key']);
  const [typeValue, setTypeValue] = useState<string | number>('ALL');
  const [showRight, setShowRight] = useState(false);

  return (
    <View className='approvals'>
      <TitleBar
        title={ApprovalStatus.find((i) => i.key === currenTabValue)?.title}
        onNavBack={() => {
          Taro.reLaunch({ url: '/pages/index/index' });
        }}
      />
      <View className='approvals-filter' style={{ marginTop: navHeight + 20 }}>
        <Tabs
          className='approvals-filter_tabs'
          value={typeValue}
          title={() => {
            return approvalList.map((item) => (
              <View
                key={item.key}
                className={`nut-tabs-titles-item ${
                  typeValue === item.key ? 'nut-tabs-titles-item-active' : ''
                }`}
                onClick={() => setTypeValue(item.key)}
              >
                <Badge color='red' top='22' right='0' dot={approvalData?.[item.key] > 0}>
                  <Text className='nut-tabs-titles-item-text'>{item.title}</Text>
                </Badge>
                <Text className='nut-tabs-titles-item-line' />
              </View>
            ));
          }}
        >
          {approvalList.map((item) => (
            <Tabs.TabPane title={item.title} value={item.key} key={item.key} />
          ))}
        </Tabs>
        <Button
          onClick={() => {
            setShowRight(true);
          }}
          className='approvals-filter_btn'
          fill='none'
          icon={<IconFont name='more' size={27} />}
        />
      </View>
      <View className='approvals-list' style={{ height: listHeight }}>
        <ApprovalList typeValue={typeValue} currenTabValue={currenTabValue} />
      </View>
      <Tabbar
        fixed
        onSwitch={(value) => {
          setTabValue(ApprovalStatus[value]['key']);
          setTypeValue('ALL');
        }}
        defaultValue={0}
      >
        <Tabbar.Item title='待办' icon={<Comment size={22} />} value={approvalCount} />
        <Tabbar.Item title='已办' icon={<Success size={22} />} />
        <Tabbar.Item title='已发起' icon={<ArrowCircleUp size={22} />} />
      </Tabbar>
      <Popup
        visible={showRight}
        className='approvals-popup'
        position='right'
        onClose={() => {
          setShowRight(false);
        }}
      >
        <View className='approvals-popup_content' style={{ marginTop: navHeight + 40 }}>
          <Text className='approvals-popup_content_title'>审批分类</Text>
          <Grid columns={2}>
            {approvalList.map((item) => {
              return (
                <Grid.Item
                  key={item.key}
                  onClick={() => {
                    setTypeValue(item.key);
                    setShowRight(false);
                  }}
                >
                  <Badge
                    color='red'
                    top='5'
                    right='10'
                    value={approvalData?.[item.key] || 0}
                    style={{ width: '100%' }}
                  >
                    <View className='approvals-popup_content_btn'>{item.title}</View>
                  </Badge>
                </Grid.Item>
              );
            })}
          </Grid>
        </View>
      </Popup>
    </View>
  );
};

export default Approvals;
