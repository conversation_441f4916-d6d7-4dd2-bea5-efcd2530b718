import { useEffect, useRef, useState } from 'react';
import { Avatar, Cell, InfiniteLoading, Skeleton, Tag } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useRequest } from 'ahooks';

import { approvalMobile } from '@/api/home';
import { ActiviStatus, approvalList } from '@/enums';
import { calcDayBetweenToday, getGradientColorFromCharCode } from '@/utils';

import './index.scss';

//列表每页条数
const pageSize = 20;

const ApprovalList = ({ typeValue, currenTabValue }) => {
  const [defaultList, setDefaultList] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const pageNum = useRef<number>(1);
  const { runAsync: requestList, loading } = useRequest(approvalMobile, {
    manual: true,
    onSuccess: (data: API.ResultApprovalResp) => {
      if (
        data?.data?.historicProcessInstanceRespList &&
        data.data.historicProcessInstanceRespList.length > 0
      ) {
        if (pageNum.current === 1) {
          setDefaultList(data.data.historicProcessInstanceRespList);
        } else {
          const arr = defaultList?.concat(data.data.historicProcessInstanceRespList);
          setDefaultList(arr);
        }
      } else {
        pageNum.current === 1 && setDefaultList([]);
        setHasMore(false);
      }
    },
  });

  const loadMore = async () => {
    pageNum.current += 1;
    await handleLists();
  };

  useEffect(() => {
    reHandleLists();
  }, [typeValue, currenTabValue]);

  useEffect(() => {
    Taro.eventCenter.on('APPROVAL_LIST_REFRESH', backHandleLists);
    return () => {
      Taro.eventCenter.off('APPROVAL_LIST_REFRESH');
    };
  }, []);

  //切换筛选条件请求
  const reHandleLists = () => {
    pageNum.current = 1;
    setHasMore(true);
    handleLists();
  };

  //基础请求
  const handleLists = async () => {
    await requestList({
      pageNum: pageNum.current,
      pageSize,
      approvalStatus: currenTabValue,
      type: typeValue === 'ALL' ? undefined : typeValue,
    });
  };

  //从详情页返回时请求
  const backHandleLists = async ({ approvalStatus, type }) => {
    pageNum.current = 1;
    setHasMore(true);
    await requestList({
      pageNum: pageNum.current,
      pageSize,
      approvalStatus,
      type: type === 'ALL' ? undefined : type,
    });
  };

  const refresh = async () => {
    pageNum.current = 1;
    setHasMore(true);
    await handleLists();
  };

  return (
    <ul id='scrollDemo' className='todo'>
      {loading && pageNum.current === 1 ? (
        <Skeleton rows={15} animated />
      ) : (
        <InfiniteLoading
          pullingText='松开刷新'
          loadingText='加载中'
          loadMoreText='没有更多了'
          pullRefresh
          target='scrollDemo'
          hasMore={hasMore}
          onLoadMore={loadMore}
          onRefresh={refresh}
        >
          {defaultList.map((item) => {
            const name = item.sponsor.slice(-2) || '未知';
            const title = approvalList.find((i) => i.key === item.type)?.title || '申请';
            const auditType =
              ActiviStatus.find((i) => i.key === item.activiStatus)?.title || '未知';
            const background =
              ActiviStatus.find((i) => i.key === item.activiStatus)?.background || '#E9E9E9';

            return (
              <View
                className='todo-cell_each'
                key={item.id}
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/approvals/components/ApprovalDetail/index?id=${item.id}&type=${item.type}&name=${item.sponsor}&time=${item.initiationTime}&tab=${currenTabValue}`,
                  });
                }}
              >
                <Cell.Group divider={false}>
                  <Cell
                    className='todo-cell_top'
                    title={
                      <View className='todo-cell_title'>
                        <Text>{title}</Text>
                      </View>
                    }
                    extra={
                      <Tag className='todo-cell_extra_tag' background={background}>
                        {auditType}
                      </Tag>
                    }
                  />
                  <Cell>
                    <View className='todo-cell_desc'>
                      <Text>编号：{item.documentNumber}</Text>
                      <Text>发起时间：{item.initiationTime}</Text>
                      <Text>审核人：{item.authUsers?.join(',')}</Text>
                    </View>
                  </Cell>
                  <Cell
                    className='todo-cell_footer'
                    align='center'
                    title={
                      <View className='todo-cell_avatar'>
                        <Avatar
                          size='small'
                          color='#fff'
                          style={{
                            ...getGradientColorFromCharCode(name?.charCodeAt(1)),
                            marginRight: '5px',
                            fontSize: '8px',
                          }}
                        >
                          {name}
                        </Avatar>
                        {item.sponsor}
                      </View>
                    }
                    extra={
                      <View className='todo-cell_time'>
                        {calcDayBetweenToday(item.initiationTime)}
                      </View>
                    }
                  />
                </Cell.Group>
              </View>
            );
          })}
        </InfiniteLoading>
      )}
    </ul>
  );
};

export default ApprovalList;
