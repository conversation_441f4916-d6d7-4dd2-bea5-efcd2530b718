.todo {
  --nutui-skeleton-line-border-radius: 16px;
  --nutui-skeleton-line-height: 50px;
  --nutui-avatar-small-width: 20px;
  --nutui-avatar-small-height: 20px;
  --nutui-tag-padding: 1px 6px;
  --nutui-skeleton-background: #E8E8E8;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  &-loading {
    margin: 10px 0;
  }

  .todo-cell_each {
    margin-left: 5px;
    width: 98%;
    background: #fff;
    border-radius: 10px;

    .todo-cell_top {
      --nutui-cell-padding: 13px 16px 0 16px;
    }

    .todo-cell_footer {
      --nutui-cell-padding: 4px 16px 13px 16px;
    }

    .todo-cell_title {
      font-size: 14px;
      font-weight: 500;
    }

    .todo-cell_desc {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .todo-cell_avatar {
      display: flex;
      align-items: center;
      font-size: 12px;
    }

    .todo-cell_time {
      font-size: 10px;
      color: orange;
    }
  }
}
