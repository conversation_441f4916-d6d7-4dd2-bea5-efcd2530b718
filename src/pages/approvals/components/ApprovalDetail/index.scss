.detail {
  --nutui-avatar-small-width: 20px;
  --nutui-avatar-small-height: 20px;
  --nutui-steps-finish-title-color: #333333d7;
  --nutui-steps-finish-description-color: #5656569b;
  --nutui-steps-finish-icon-bg-color: #e6fffb;
  --nutui-steps-finish-icon-color: var(--primary-color);
  --nutui-steps-process-icon-color: var(--primary-color);
  --nutui-steps-wait-icon-bg-color: #f0f0f0;
  --nutui-steps-wait-title-color: #5656569b;
  --nutui-steps-wait-description-color: #5656569b;
  --nutui-steps-wait-icon-color: #565656;
  position: relative;

  &-header {
    background: var(--primary-color);

    .nut-navbar-left {
      align-items: flex-end;
      padding-bottom: 10px;
    }
  }

  &-skeleton {
    width: calc(100vw - 60px);
    margin-top: 9px;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: var(--nutui-cell-box-shadow, 0px 1px 7px 0px rgb(237, 238, 241));
  }

  &-main {
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;

    &_title {
      width: 100%;
      height: 100px;
      padding: 10px;
      background-image: linear-gradient(to bottom, var(--primary-color), #ffffff);

      &_cell {
        width: calc(100% - 20px);
        height: 100%;
      }

      &_skeleton {
        height: calc(100% - 40px);
      }

      &_header {
        font-weight: 500;
        font-size: 14px;
      }

      &_number {
        color: #565656c5;
        font-size: 11px;
      }

      &_desc {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px 0;
      }
    }

    &_body {
      box-sizing: border-box;
      max-width: 100%;
      padding: 0 10px;
    }

    &_progress {
      width: 100%;
      padding: 0 10px;

      &_cell {
        width: calc(100% - 20px);
        margin: 0;
      }

      &_steps {
        width: 100%;
      }

      &_step {
        .nut-step-main {
          width: 100%;
          height: 100px;
          padding-right: 10px;
        }
      }

      &_desc {
        display: flex;
        justify-content: space-between;
        width: 100%;
      }

      &_right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }
    }
  }

  .detail-main_actions {
    position: fixed;
    bottom: 0;
    z-index: 10;
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    width: 100%;
    padding: 10px 0;
    background: #fff;
    box-shadow: var(--nutui-cell-box-shadow, 0px 1px 7px 0px rgb(237, 238, 241));

    &_button {
      flex: 1;
      color: var(--primary-color);
      font-size: 14px;
    }
    &_divider {
      position: relative;
      top: 5px;
      width: 1px;
      height: 20px;
      background: #e5e5e5;
    }
  }
}

.approval-textarea {
  height: 100px;
  overflow: hidden;
}
