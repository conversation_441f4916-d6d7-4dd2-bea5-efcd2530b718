import { useEffect, useState } from 'react';
import {
  ArrowLeft,
  Check,
  CheckDisabled,
  Checked,
  Close,
  Loading1,
  MaskClose,
} from '@nutui/icons-react-taro';
import {
  Avatar,
  Button,
  Cell,
  Dialog,
  NavBar,
  Skeleton,
  Space,
  Step,
  Steps,
  Tag,
  TextArea,
} from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useRequest } from 'ahooks';

import { agree, getActiviInfoMobile, refuse, reject } from '@/api/flow';
import { ActiviStatus, approvalList } from '@/enums';
import { useSafeStatusbar } from '@/hooks';
import { LeaveApplication } from '@/pages/approvals/components/ApprovalDetail/components/LeaveApplication';
import { useLoginStore } from '@/store';
import {
  calcAvailableBottom,
  calcAvailableHeight,
  getGradientColorFromCharCode,
  isValueNotEmpty,
} from '@/utils';

import { AfterAndSalesAndDevelopProject } from './components/AfterAndSalesAndDevelopProject';
import { Announcement } from './components/Announcement';
import { BidFiling } from './components/BidFiling';
import { ContractPayment } from './components/ContractPayment';
import { GeneralPayment } from './components/GeneralPayment';
import { InnerContract } from './components/InnerContract';
import { LeaveSell } from './components/LeaveSell';
import { MainContract } from './components/MainContract';
import { Partner } from './components/Partner';
import { PreAndInnerProject } from './components/PreAndInnerProject';
import { PurchaseContract } from './components/PurchaseContract';
import { RecruitmentApplication } from './components/RecruitmentApplication';
import { Reimbursement } from './components/Reimbursement';
import { ResourceApplication } from './components/ResourceApplication';
import { TrainingReimbursement } from './components/TrainingReimbursement';
import { WorkOrder } from './components/WorkOrder';

import './index.scss';

const listHeight = calcAvailableHeight(0, 44) + 'px';
const aBottom = calcAvailableBottom();
const bottomHeight = isValueNotEmpty(aBottom) ? (aBottom as number) : 0;

const ApprovalDetail = () => {
  const [info, setInfo] = useState<API.ActiviInfoResp>();
  const [comments, setComments] = useState('');
  const [commentVisible, setCommentVisible] = useState(false);
  const [commentType, setCommentType] = useState('');

  const $instance = useRouter().params;
  const approvalType = approvalList.find((i) => i.key === $instance.type)?.title || '未知';
  const avatarName = $instance?.name?.slice(-2) || '未知';

  const navHeight = useSafeStatusbar();
  const store = useLoginStore();

  const { run: requestDetail } = useRequest(getActiviInfoMobile, {
    manual: true,
    onSuccess: (data: API.ResultActiviInfoResp) => {
      const { fromData, processType } = data?.data as API.ActiviInfoResp & { processType?: string };
      let baseNumber;
      switch (processType) {
        case 'TRAINING_REIMBURSEMENT_APPROVAL':
          baseNumber = fromData?.trainNumber;
          break;
        case 'PROJECT_APPROVAL_SH':
          baseNumber = fromData?.proBaseInfo?.projectNumber;
          break;
        case 'PROJECT_APPROVAL_SQ':
          baseNumber = fromData?.proBaseInfo?.projectNumber;
          break;
        case 'PROJECT_APPROVAL_KF':
          baseNumber = fromData?.proBaseInfo?.projectNumber;
          break;
        case 'PROJECT_APPROVAL_XS':
          baseNumber = fromData?.proBaseInfo?.projectNumber;
          break;
        case 'PROJECT_APPROVAL_NB':
          baseNumber = fromData?.proBaseInfo?.projectNumber;
          break;
        case 'BUSINESS_PARTNER':
          baseNumber = fromData?.partnerInfo?.clientNumber;
          break;
        case 'CONTRACT_PROCUREMENT_PAYMENT_APPLICATION':
          baseNumber = fromData?.applicationNumber;
          break;
        case 'MAIN_CONTRACT_APPROVAL':
          baseNumber = fromData?.mainContractInfo?.contractNumber;
          break;
        case 'PURCHASE_CONTRACT_APPROVAL':
          baseNumber = fromData?.purContractInfo?.contractNumber;
          break;
        case 'INTERNAL_CONTRACT_APPROVAL':
          baseNumber = fromData?.innerConInfo?.contractNumber;
          break;
        case 'ANNOUNCEMENT_APPROVAL':
          baseNumber = fromData?.title;
          break;
        case 'BID_FILING_APPROVAL':
          baseNumber = fromData?.documentNumber;
          break;
        default:
          baseNumber = fromData?.documentNumber;
      }
      const infoData = { ...data?.data, fromData: { ...fromData, documentNumber: baseNumber } };
      setInfo(infoData);
    },
  });

  // 通过流程
  const { run: runAgree, loading: agreeLoading } = useRequest(
    () =>
      agree({
        processInstanceId: $instance.id,
      }),
    {
      manual: true,
      onSuccess: () => {
        Taro.showToast({
          title: '操作成功',
          icon: 'success',
        });
        handleDetail();
      },
    },
  );

  // 拒绝流程
  const { run: runRefuse, loading: refuseLoading } = useRequest(
    () =>
      refuse({
        processInstanceId: $instance.id,
        type: $instance.type,
        comments,
      }),
    {
      manual: true,
      onSuccess: () => {
        Taro.showToast({
          title: '拒绝成功',
          icon: 'success',
        });
        setCommentVisible(false);
        handleDetail();
      },
    },
  );
  // 驳回流程
  const { run: runReject, loading: rejectLoading } = useRequest(
    () =>
      reject({
        processInstanceId: $instance.id,
        type: $instance.type,
        comments,
      }),
    {
      manual: true,
      onSuccess: () => {
        Taro.showToast({
          title: '驳回成功',
          icon: 'success',
        });
        setCommentVisible(false);
        handleDetail();
      },
    },
  );

  useEffect(() => {
    handleDetail();
  }, []);

  const handleDetail = () => {
    requestDetail({ id: $instance?.id as string });
  };

  const handleConfirm = () => {
    if (commentType === 'refuse') {
      runRefuse();
    } else {
      runReject();
    }
  };

  return (
    <View className='detail'>
      <NavBar
        back={
          <>
            <ArrowLeft size={20} color='#fff' />
          </>
        }
        className='detail-header'
        fixed
        onBackClick={() => {
          Taro.eventCenter.trigger('APPROVAL_LIST_REFRESH', {
            approvalStatus: $instance.tab,
            type: $instance.type,
          });
          Taro.navigateBack();
        }}
        style={{
          // @ts-ignore
          '--nutui-navbar-height': navHeight + 44 + 'px',
        }}
      ></NavBar>
      <View
        className='detail-main'
        style={{
          height: listHeight,
          marginTop: navHeight + 43,
        }}
      >
        <View className='detail-main_title'>
          {info ? (
            <Cell
              className='detail-main_title_cell'
              title={
                <Space className='detail-main_title_header'>
                  {approvalType}
                  <Tag
                    className='todo-cell_extra_tag'
                    background={
                      ActiviStatus.find((i) => i.key === info.activiStatus)?.background || '#E9E9E9'
                    }
                  >
                    {ActiviStatus.find((i) => i.key === info.activiStatus)?.title || '未知'}
                  </Tag>
                </Space>
              }
              extra={
                <View className='detail-main_title_number'>{info.fromData?.documentNumber}</View>
              }
              description={
                <View className='detail-main_title_desc'>
                  <Space className='detail-main_title_avatar'>
                    <Avatar
                      size='small'
                      color='#fff'
                      style={{
                        ...getGradientColorFromCharCode(avatarName?.charCodeAt(1)),
                        fontSize: '8px',
                      }}
                    >
                      {avatarName}
                    </Avatar>
                    <Text>{$instance.name}</Text>
                  </Space>
                  <Text>{`提交于${$instance.time}`}</Text>
                </View>
              }
            />
          ) : (
            <Skeleton
              className='detail-skeleton detail-main_title_skeleton'
              rows={2}
              title
              animated
            />
          )}
        </View>

        <View className='detail-main_body'>
          {info ? (
            <>
              {$instance.type === 'LEAVE_APPROVAL_PROCESS' && <LeaveApplication info={info} />}
              {$instance.type === 'REVOCATION_APPROVAL' && <LeaveSell info={info} />}
              {$instance.type === 'RESOURCE_APPLICATION' && <ResourceApplication info={info} />}
              {$instance.type === 'RECRUITMENT_APPLICATION' && (
                <RecruitmentApplication info={info} />
              )}
              {$instance.type === 'REIMBURSEMENT_APPROVAL' && <Reimbursement info={info} />}
              {$instance.type === 'TRAINING_REIMBURSEMENT_APPROVAL' && (
                <TrainingReimbursement info={info} />
              )}
              {$instance.type === 'WORK_ORDER_APPROVAL' && <WorkOrder info={info} />}
              {$instance.type === 'UNIVERSAL_PAYMENT' && <GeneralPayment info={info} />}
              {$instance.type === 'CONTRACT_PROCUREMENT_PAYMENT_APPLICATION' && (
                <ContractPayment info={info} />
              )}
              {$instance.type === 'PROJECT_APPROVAL_NB' && <PreAndInnerProject info={info} />}
              {$instance.type === 'PROJECT_APPROVAL_SQ' && <PreAndInnerProject info={info} />}
              {$instance.type === 'PROJECT_APPROVAL_XS' && (
                <AfterAndSalesAndDevelopProject info={info} />
              )}
              {$instance.type === 'PROJECT_APPROVAL_KF' && (
                <AfterAndSalesAndDevelopProject info={info} />
              )}
              {$instance.type === 'PROJECT_APPROVAL_SH' && (
                <AfterAndSalesAndDevelopProject info={info} />
              )}
              {$instance.type === 'BUSINESS_PARTNER' && <Partner info={info} />}
              {$instance.type === 'MAIN_CONTRACT_APPROVAL' && <MainContract info={info} />}
              {$instance.type === 'PURCHASE_CONTRACT_APPROVAL' && <PurchaseContract info={info} />}
              {$instance.type === 'INTERNAL_CONTRACT_APPROVAL' && <InnerContract info={info} />}
              {$instance.type === 'ANNOUNCEMENT_APPROVAL' && <Announcement info={info} />}
              {$instance.type === 'BID_FILING_APPROVAL' && <BidFiling info={info} />}
            </>
          ) : (
            <Skeleton className='detail-skeleton' rows={18} title animated />
          )}
        </View>

        <View className='detail-main_progress' style={{ marginBottom: bottomHeight + 70 + 'px' }}>
          {info?.audiRecordList ? (
            <Cell className='detail-main_progress_cell'>
              <Steps
                direction='vertical'
                value={
                  info.audiRecordList?.findIndex((item) =>
                    ['process', 'error'].includes(item.status as string),
                  ) + 1
                }
                className='detail-main_progress_steps'
              >
                {info?.audiRecordList.map((item, index) => {
                  return (
                    <Step
                      style={{
                        // @ts-ignore
                        '--nutui-steps-process-icon-bg-color':
                          item.status === 'error' ? '#FA4D4F' : 'transparent',
                        '--nutui-steps-process-title-color':
                          item.status === 'error' ? '#FA4D4F' : '#333333d7',
                      }}
                      className='detail-main_progress_step'
                      value={index + 1}
                      key={index + 1}
                      title={item.title}
                      icon={
                        item.status === 'finish' ? (
                          <Check size={15} />
                        ) : item.status === 'process' ? (
                          <Loading1 size={30} />
                        ) : item.status === 'error' ? (
                          <Close size={15} color='#fff' />
                        ) : undefined
                      }
                      description={
                        <View
                          className='detail-main_progress_desc'
                          style={{ color: item.status === 'error' ? '#FA4D4F' : undefined }}
                        >
                          <Text>{item.userName?.join(',') || ''}</Text>
                          <View className='detail-main_progress_right'>
                            <Text>{item.audiTime}</Text>
                            <Text>{item.comments}</Text>
                          </View>
                        </View>
                      }
                    />
                  );
                })}
              </Steps>
            </Cell>
          ) : (
            <Skeleton className='detail-skeleton' rows={3} title animated />
          )}
        </View>
      </View>
      {$instance.tab === 'AGENT' &&
        info?.audiRecordList
          ?.find((i) => i.status === 'process')
          ?.userName?.includes(store.userInfo?.username as string) && (
          <View className='detail-main_actions' style={{ height: 30 + bottomHeight + 'px' }}>
            <Button
              icon={<CheckDisabled color='var(--primary-color)' size='16' />}
              loading={rejectLoading}
              shape='square'
              fill='none'
              className='detail-main_actions_button'
              onClick={() => {
                setComments('');
                setCommentType('reject');
                setCommentVisible(true);
              }}
            >
              驳回
            </Button>
            <View className='detail-main_actions_divider' />
            <Button
              icon={<MaskClose color='var(--primary-color)' size='16' />}
              loading={refuseLoading}
              onClick={() => {
                setComments('');
                setCommentType('refuse');
                setCommentVisible(true);
              }}
              shape='square'
              fill='none'
              className='detail-main_actions_button'
            >
              拒绝
            </Button>
            <View className='detail-main_actions_divider' />
            <Button
              icon={<Checked color='var(--primary-color)' size='16' />}
              loading={agreeLoading}
              onClick={runAgree}
              shape='square'
              fill='none'
              className='detail-main_actions_button'
            >
              通过
            </Button>
          </View>
        )}
      <Dialog
        title={commentType === 'refuse' ? '拒绝意见' : '驳回意见'}
        visible={commentVisible}
        id='comment'
        onConfirm={handleConfirm}
        onCancel={() => setCommentVisible(false)}
      >
        <TextArea
          className='approval-textarea'
          rows={1}
          value={comments}
          onChange={(value) => setComments(value)}
          showCount
          maxLength={20}
        />
      </Dialog>
    </View>
  );
};

export default ApprovalDetail;
