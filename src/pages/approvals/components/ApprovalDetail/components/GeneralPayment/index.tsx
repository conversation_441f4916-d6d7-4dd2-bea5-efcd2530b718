import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { PAYMENT, PAYMENT_TYPE } from '@/enums/web';
import { getObject } from '@/utils';

import { generalPropsKeys } from '../PropsKeys';

import '../index.scss';

export const GeneralPayment = ({ info }) => {
  const { paymentType, paymentMethod } = info?.fromData;
  const data = {
    ...info?.fromData,
    paymentType: getObject(PAYMENT_TYPE)[paymentType],
    paymentMethod: getObject(PAYMENT)[paymentMethod],
  };

  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(generalPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{generalPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
