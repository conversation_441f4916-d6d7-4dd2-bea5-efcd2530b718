import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import '../index.scss';

const propsKeys = {
  employee: '员工编号',
  employeeName: '员工姓名',
  department: '部门',
  leaveTypeName: '休假类型',
  leaveBalance: '可用天数',
  fromDate: '开始日期',
  toDate: '结束日期',
  halfDate: '上半天班日期',
  totalLeaveDays: '总休假天数',
  description: '原因',
};

export const LeaveApplication = ({ info }) => {
  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(propsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{propsKeys[key]}</View>
                <View className='common-detail_each_right'>{info.fromData[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
