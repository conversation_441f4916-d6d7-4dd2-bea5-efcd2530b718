import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { innerContractPropsKeys } from '../PropsKeys';

import '../index.scss';

export const InnerContract = ({ info }) => {
  const { innerConInfo } = info?.fromData;
  const data = {
    ...innerConInfo,
    contractAmount: parseFloat(innerConInfo?.contractAmount).toFixed(2),
    contractTax: parseFloat(innerConInfo?.contractTax).toFixed(2),
  };
  if (['执行中', '暂停'].includes(data?.contractStatus)) {
    delete data.actuallyStopTime;
    delete data.actuallyEndTime;
  }

  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(innerContractPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{innerContractPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
