import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { PAYMENT } from '@/enums/web';
import { getObject } from '@/utils';

import { contractPropsKeys } from '../PropsKeys';

import '../index.scss';

export const ContractPayment = ({ info }) => {
  const { payWay } = info?.fromData;
  const data = {
    ...info?.fromData,
    payWay: getObject(PAYMENT)[payWay],
  };
  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(contractPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{contractPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
