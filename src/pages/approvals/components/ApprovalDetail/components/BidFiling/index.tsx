import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { BIDDING_TYPE, COOPERATION_WAYS } from '@/enums';
import { getObject } from '@/utils';

import { bidFilingPropsKeys } from '../PropsKeys';

import '../index.scss';

export const BidFiling = ({ info }) => {
  const { bidType, bidMethod, amount, deposit } = info?.fromData;

  const data = {
    ...info?.fromData,
    bidType: getObject(COOPERATION_WAYS)[bidType],
    bidMethod: getObject(BIDDING_TYPE)[bidMethod],
    amount: amount ? `¥${Number(amount).toLocaleString()}` : '¥0',
    deposit: deposit ? `¥${Number(deposit).toLocaleString()}` : '¥0',
  };

  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(bidFilingPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{bidFilingPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
