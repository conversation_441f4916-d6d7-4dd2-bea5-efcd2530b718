import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { recruitmentPropsKeys } from '../PropsKeys';

import '../index.scss';

export const RecruitmentApplication = ({ info }) => {
  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(recruitmentPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{recruitmentPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{info.fromData[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
