import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { COOPERATION_WAYS, INDUSTRY } from '@/enums/web';
import { getObject } from '@/utils';

import { partnerPropsKeys } from '../PropsKeys';

import '../index.scss';

const PARTNER_TYPES = {
  CLIENT: '客户',
  VENDOR: '供应商',
  INSTITUTION: '招标机构',
};

export const Partner = ({ info }) => {
  const { clientMangers, clientManger, industry, cooperateWay, partnerType } =
    info.fromData?.partnerInfo;
  const managers = clientMangers?.map((item) => item.clientMangerName)?.join('、');
  const manager = clientMangers && clientMangers?.length !== 0 ? managers : clientManger;
  const data = {
    ...info.fromData?.partnerInfo,
    clientManger: manager,
    industry: getObject(INDUSTRY)[industry],
    cooperateWay: getObject(COOPERATION_WAYS)[cooperateWay],
  };
  const PropsKeys = {
    ...partnerPropsKeys,
    clientName: `${PARTNER_TYPES[partnerType]}名称`,
    cooperateWay: partnerType === 'CLIENT' ? '客户类型' : '',
  };

  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(PropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{PropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
