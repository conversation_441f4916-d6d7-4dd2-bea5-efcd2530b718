import { Link } from '@nutui/icons-react-taro';
import { Cell } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';

import apiConfig from '@/services/api.config';

import { workOderPropsKeys } from '../PropsKeys';

import './index.scss';

export const WorkOrder = ({ info }) => {
  const { fileName, projectType } = info?.fromData;
  const data = {
    ...info?.fromData,
    projectType: projectType === 'SH' ? '售后项目' : '售前项目',
  };

  //预览
  const Preview = async (file) => {
    Taro.showLoading({ title: '加载中...', mask: true });
    const isPdf = file?.includes('.pdf');
    const fs = Taro.getFileSystemManager();
    const tempFilePath = `${Taro.env.USER_DATA_PATH}/${file.split('-')[1]}`; // 创建临时文件路径

    Taro.request({
      url: `${apiConfig.baseUrl}/api/common/file/image/preview/`,
      data: { fileName: file },
      method: 'POST',
      responseType: 'arraybuffer',
      success: (res) => {
        fs.writeFileSync(tempFilePath, res?.data); // 将二进制数据写入文件
        const preview =
          isPdf === true
            ? Taro.openDocument({
                filePath: tempFilePath,
                fileType: 'pdf',
                success: (res1) => {
                  Taro.hideLoading();
                  console.log('pdf success', res1);
                },
                fail: (res1) => {
                  Taro.hideLoading();
                  console.log('pdf fail', res1);
                },
                complete: (res1) => {
                  Taro.hideLoading();
                  console.log('pdf接口调用结束', res1);
                },
              })
            : Taro.previewImage({
                current: tempFilePath,
                urls: [tempFilePath],
                success: (res2) => {
                  Taro.hideLoading();
                  console.log('success', res2);
                },
                fail: (res2) => {
                  Taro.hideLoading();
                  console.log('fail', res2);
                },
                complete: (res2) => {
                  Taro.hideLoading();
                  console.log('接口调用结束', res2);
                },
              });
        return preview;
      },
    });
    fs.unlinkSync(tempFilePath); //删除临时文件
  };

  return (
    <Cell
      className='workOder'
      title={<View className='workOder-title'>审批详情</View>}
      description={
        <View className='workOder-detail'>
          {Object.keys(workOderPropsKeys).map((key) => {
            return (
              <View className='workOder-detail_each' key={key}>
                <View className='workOder-detail_each_left'>{workOderPropsKeys[key]}</View>
                <View className='workOder-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
          <Text className='workOder-detail_each_left'>工单附件</Text>
          {fileName?.map((item, index) => {
            return (
              <View className='workOder-detail_file' key={index} onTap={() => Preview(item)}>
                <Link size={13} style={{ marginRight: '10px' }} />
                <Text>{item.split('-')[1]}</Text>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
