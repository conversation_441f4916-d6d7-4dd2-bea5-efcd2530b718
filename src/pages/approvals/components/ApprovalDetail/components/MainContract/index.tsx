import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { mainContractPropsKeys } from '../PropsKeys';

import '../index.scss';

export const MainContract = ({ info }) => {
  const { mainContractInfo } = info?.fromData;
  const data = {
    ...mainContractInfo,
    contractAmount: parseFloat(mainContractInfo?.contractAmount).toFixed(2),
    isFrameworkAgreement: mainContractInfo?.isFrameworkAgreement === 1 ? '是' : '否',
  };
  if (['执行中', '暂停'].includes(data?.contractStatus)) {
    delete data.actuallyStopTime;
    delete data.actuallyEndTime;
  }

  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(mainContractPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{mainContractPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
