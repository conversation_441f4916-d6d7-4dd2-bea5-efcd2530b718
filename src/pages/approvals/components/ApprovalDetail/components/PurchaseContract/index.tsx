import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { purchaseContractPropsKeys } from '../PropsKeys';

import '../index.scss';

export const PurchaseContract = ({ info }) => {
  const { purContractInfo } = info?.fromData;
  const data = {
    ...purContractInfo,
    contractAmount: parseFloat(purContractInfo?.contractAmount).toFixed(2),
    contractTax: parseFloat(purContractInfo?.contractTax).toFixed(2),
    mainAmount: parseFloat(purContractInfo?.mainAmount).toFixed(2),
    difference: parseFloat(purContractInfo?.difference).toFixed(2),
    isFrameworkAgreement: purContractInfo?.isFrameworkAgreement === 1 ? '是' : '否',
  };
  if (['执行中', '暂停'].includes(data?.contractStatus)) {
    delete data.actuallyStopTime;
    delete data.actuallyEndTime;
  }

  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(purchaseContractPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{purchaseContractPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
