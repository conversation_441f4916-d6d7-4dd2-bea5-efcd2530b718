import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { leaveSellPropsKeys } from '../PropsKeys';

import '../index.scss';

export const LeaveSell = ({ info }) => {
  const { half } = info?.fromData;
  const halfObj = { 0: '否', 1: '是' };
  const data = { ...info?.fromData, half: halfObj[half] };
  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(leaveSellPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{leaveSellPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
