import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { announcementPropsKeys } from '../PropsKeys';

import '../index.scss';

export const Announcement = ({ info }) => {
  const { fromData } = info;

  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(announcementPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{announcementPropsKeys[key]}</View>
                {key === 'content' ? (
                  <div
                    style={{ overflow: 'hidden' }}
                    className='common-detail_each_right'
                    dangerouslySetInnerHTML={{ __html: fromData[key] || '' }}
                  ></div>
                ) : (
                  <View className='common-detail_each_right'>{fromData[key] || ''}</View>
                )}
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
