/**
 * 销假申请
 */
export const leaveSellPropsKeys = {
  leaveAppNumber: '休假申请编号',
  leaveType: '休假类型',
  fromDate: '休假开始时间',
  toDate: '休假结束时间',
  totalLeaveDays: '总休假天数',
  sellDate: '销假日期',
  half: '是否销假半天',
};

/**
 * 资源使用申请
 */
export const ResourcePropsKeys = {
  employee: '员工编号',
  employeeName: '员工姓名',
  content: '申请内容',
  resourceDepartment: '资源所属部门',
  useTime: '预计使用时间',
  stillTime: '预计归还时间',
  useCause: '使用原因',
};
/**
 * 招聘申请
 */
export const recruitmentPropsKeys = {
  applicantName: '申请人',
  applicationTime: '申请日期',
  expectationTime: '期望到岗日期',
  positionNameId: '需求岗位名称',
  positionNum: '需求人数',
  affiliationDepartmentId: '隶属部门',
  demandGrade: '需求岗位等级',
  territory: '工作属地',
  applicationCause: '申请原因',
};

/**
 * 培训报销
 */
export const trainingPropsKeys = {
  employeeNumber: '员工编号',
  employeeName: '员工姓名',
  trainSpend: '培训费用',
  operateState: '发票',
  count: '返还期数',
  remainderCount: '剩余返还月',
  startTime: '返还起始月',
  endTime: '返还结束月',
  remainderAmount: '剩余返还金额',
  nextYearAmount: '次年返还金额',
  amount: '每月返还额',
  state: '返还状态',
};

/**
 * 项目付款
 */
export const generalPropsKeys = {
  username: '申请人',
  departmentName: '所属部门',
  applicationDate: '申请日期',
  paymentType: '付款类型',
  paymentMethod: '付款方式',
  expectedPaymentDate: '预计付款日期',
  paymentDeadline: '付款截止日期',
  projectName: '项目名称',
  customerName: '客户名称',
  remarks: '说明',

  accountName: '单位名称(账户名)',
  openingBank: '开户行',
  account: '银行账号',
  paymentAmount: '付款金额',
  directions: '备注',
};
/**
 * 合同付款
 */
export const contractPropsKeys = {
  contractName: '合同名称',
  estimatePayTime: '计划付款日期',
  institutionName: '单位名称(账户名)',
  bank: '开户银行',
  account: '银行账号',
  payWay: '付款方式',
  payAmount: '付款金额',
  countInvoicedTime: '对方开票日期',
  receiptAmount: '收票金额',
  remarks: '备注',
  useWay: '用途',
};

/**
 * 售前、内部项目    售后、销售项目多了contractName: '合同名称',
 */
export const commonProjectPropsKeys = {
  projectName: '项目名称',
  projectType: '项目类型',
  salePerson: '销售经理',
  projectManger: '项目经理',
  clientName: '客户名称',
  startTime: '开始日期',
  endTime: '结束日期',
  executeDepartment: '执行部门',
  status: '项目状态',
  projectAddress: '项目所在地',
  costEstimate: '成本估算',
};
/**
 cooperateWay: '客户类型',仅客户存在
 * 业务伙伴
 */
export const partnerPropsKeys = {
  clientName: '客户名称',
  clientManger: '客户经理',
  city: '所在城市',
  industry: '行业',
};

/**
 * 工单
 */
export const workOderPropsKeys = {
  type: '工单类型',
  projectType: '项目类型',
  projectNumber: '项目编号',
  projectName: '项目名称',
  employeeNumber: '工程师工号',
  employeeName: '工程师姓名',
  startTime: '开始时间',
  endTime: '结束时间',
  contactName: '联系人姓名',
  contactPhone: '联系人电话',
  workHours: '工时',
  describe: '事件描述',
  content: '工作内容',
  nextPlan: '下一步计划',
};

/**
 * 主合同
 */
export const mainContractPropsKeys = {
  contractNumber: '合同编号',
  contractName: '合同名称',
  contractCategory: '合同类别',
  serveCategory: '服务类别',
  contractQuality: '合同性质',
  fpName: '甲方',
  spName: '乙方',
  endUser: '最终用户',
  industry: '行业',
  salePerson: '销售',
  projectNumber: '售前项目编号',
  contractAmount: '合同金额',
  isFrameworkAgreement: '框架协议',
  contractStatus: '合同状态',
  actuallyStopTime: '终止日期',
  actuallyEndTime: '结束日期',
  contractAddress: '合同所在地',
  customerAbbreviation: '客户简称',
  startTime: '开始日期',
  endTime: '计划结束日期',
  signDate: '签订日期',
  signStatus: '签订状态',
  overview: '内容概述',
};

/**
 * 采购合同
 */
export const purchaseContractPropsKeys = {
  contractNumber: '合同编号',
  contractName: '合同名称',
  serveCategory: '服务类别',
  fpName: '甲方',
  spName: '乙方',
  endUser: '最终用户',
  industry: '行业',
  mainConNumber: '关联主合同编号',
  mainConName: '关联主合同名称',
  mainAmount: '关联主合同金额',
  salePerson: '销售',
  contractAmount: '合同金额',
  contractTax: '总税额',
  isFrameworkAgreement: '框架协议',
  contractStatus: '合同状态',
  actuallyStopTime: '终止日期',
  actuallyEndTime: '结束日期',
  contractAddress: '合同所在地',
  startTime: '开始日期',
  endTime: '计划结束日期',
  signDate: '签订日期',
  estimatedDeliDate: '计划交货日期',
  difference: '差额',
  signStatus: '签订状态',
  overview: '内容概述',
};
/**
 * 内部合同
 */
export const innerContractPropsKeys = {
  contractNumber: '合同编号',
  contractName: '合同名称',
  serveCategory: '服务类别',
  execDepartment: '执行部门',
  projectNumber: '项目编号',
  fpName: '甲方',
  spName: '乙方',
  proManger: '项目经理',
  contractAmount: '合同金额',
  contractTax: '总税额',
  contractStatus: '合同状态',
  actuallyStopTime: '终止日期',
  actuallyEndTime: '结束日期',
  contractAddress: '合同所在地',
  startTime: '开始日期',
  endTime: '计划结束日期',
  signDate: '签订日期',
  estimatedDeliDate: '计划交货日期',
  signStatus: '签订状态',
  overview: '内容概述',
};
/**
 * 公告
 */
export const announcementPropsKeys = {
  title: '标题',
  content: '公告内容',
};

/**
 * 投标项目报备
 */
export const bidFilingPropsKeys = {
  reportDate: '报备日期',
  salesPersonName: '销售',
  businessName: '商务人员',
  customerName: '客户名称',
  bidProjectName: '项目名称',
  amount: '金额',
  linkRequired: '链接',
  bidType: '类型',
  deposit: '保证金',
  signupDeadline: '报名截止日期',
  bidDate: '投标日期',
  bidMethod: '投标方式',
};
