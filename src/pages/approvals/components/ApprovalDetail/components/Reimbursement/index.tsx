import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import DayInfoDetail from './components/DayInfoDetail';
import TravelDetail from './components/TravelDetail';
import TravelInfoDetail from './components/TravelInfoDetail';

import './index.scss';

const reimbursementPropsKeys = {
  employee: '员工编号',
  employeeName: '员工姓名',
  department: '部门',
  type: '报销类型',
  actual: '实际金额',
  allowance: '补贴金额',
  actualTotal: '实际总金额',
  total: '报销总金额',
};

export const Reimbursement = ({ info }) => {
  const { dayInfoList, busiProjList, busiInfoList, type } = info?.fromData;
  const obj =
    info?.fromData?.type === 'RC'
      ? { ...info.fromData, type: '日常报销' }
      : { ...info.fromData, type: '差旅报销' };

  return (
    <Cell
      className='reimbursement'
      title={<View className='reimbursement-title'>审批详情</View>}
      description={
        <>
          <View className='reimbursement-detail'>
            {Object.keys(reimbursementPropsKeys).map((key) => {
              const value = obj[key];
              const formattedValue = key === 'actualTotal' ? parseFloat(value).toFixed(2) : value;

              return (
                <View className='reimbursement-detail_each' key={key}>
                  <View className='reimbursement-detail_each_left'>
                    {reimbursementPropsKeys[key]}
                  </View>
                  <View className='reimbursement-detail_each_right'>{formattedValue || ''}</View>
                </View>
              );
            })}
            {type === 'RC' && <DayInfoDetail dayInfoList={dayInfoList} />}
            {type === 'CL' && (
              <>
                <TravelInfoDetail travelInfoList={busiInfoList} />
                <TravelDetail travelInfo={busiProjList} />
              </>
            )}
          </View>
        </>
      }
    ></Cell>
  );
};
