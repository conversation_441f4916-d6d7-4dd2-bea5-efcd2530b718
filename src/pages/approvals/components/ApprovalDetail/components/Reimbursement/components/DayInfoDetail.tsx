import { useState } from 'react';
import { Button, Ellipsis, Popup, Table } from '@nutui/nutui-react-taro';
import { TableColumnProps } from '@nutui/nutui-react-taro/dist/types/packages/table/types';
import { Text, View } from '@tarojs/components';

import { useSafeStatusbar } from '@/hooks';

import '../index.scss';

const columnsType = {
  projectNumber: '项目编号',
  projectName: '项目名称',
  time: '日期',
  type: '费用类型',
  money: '金额',
  actualType: '实际报销类型',
  actualMoney: '实际报销金额',
  reason: '正当理由',
  remark: '备注',
};

const DayInfoDetail = ({ dayInfoList }) => {
  const navHeight = useSafeStatusbar();
  const [visible, setVisible] = useState(false);
  const [initRecord, setInitRecord] = useState({});

  const columns: TableColumnProps[] = [
    {
      title: '日期',
      key: 'time',
    },
    {
      title: '实际报销类型',
      key: 'actualType',
    },
    {
      title: '实际报销金额',
      key: 'actualMoney',
    },
    {
      title: '操作',
      key: 'render',
      render: (rowData) => {
        return (
          <Button
            fill='none'
            type='info'
            onClick={() => {
              setVisible(true);
              setInitRecord(rowData);
            }}
            style={{ fontSize: '12px', display: 'flex' }}
          >
            详情
          </Button>
        );
      },
      width: 60,
      fixed: 'right',
      align: 'center',
    },
  ];

  return (
    <>
      <View className='reimbursement-detail_each'>
        <Text className='reimbursement-detail_each_left'>日常报销明细</Text>
      </View>
      <Table
        columns={columns}
        data={dayInfoList || []}
        striped
        style={{
          fontSize: '12px',
          maxWidth: '100%',
          boxSizing: 'border-box',
        }}
        bordered
      />
      <Popup
        visible={visible}
        position='right'
        destroyOnClose
        onClose={() => {
          setVisible(false);
        }}
        className='popup'
        title='详情'
      >
        <View className='popup-content' style={{ marginTop: navHeight }}>
          <Text className='popup-content_title'>详情</Text>
          {Object.keys(columnsType)?.map((key) => {
            return (
              <View key={key} className='popup-detail_each'>
                <Text className='popup-detail_each_left'>{columnsType[key] || ''}</Text>
                <Ellipsis
                  content={initRecord[key] || ''}
                  expandText='展开'
                  collapseText='收起'
                  direction='end'
                  className='popup-detail_each_right'
                />
              </View>
            );
          })}
        </View>
      </Popup>
    </>
  );
};

export default DayInfoDetail;
