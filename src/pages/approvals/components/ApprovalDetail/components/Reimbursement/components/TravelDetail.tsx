import { useState } from 'react';
import { Button, Ellipsis, Popup, Table } from '@nutui/nutui-react-taro';
import { TableColumnProps } from '@nutui/nutui-react-taro/dist/types/packages/table/types';
import { Text, View } from '@tarojs/components';

import { ProjectTypes } from '@/enums';
import { useSafeStatusbar } from '@/hooks';

import '../index.scss';

const columnsType = {
  projectNumber: '项目编号',
  projectName: '项目名称',
  projectType: '项目类型',
  workDesc: '出差事由',
  goDate: '出发时间',
  leave: '出发地',
  backDate: '返程时间',
  arrive: '到达地',
  days: '停留天数',
  allowance: '补贴金额',
  remark: '备注',
};

const TravelDetail = ({ travelInfo }) => {
  const navHeight = useSafeStatusbar();
  const [visible, setVisible] = useState<boolean>(false);
  const [initRecord, setInitRecord] = useState<Record<string, any>>({});

  const columns: TableColumnProps[] = [
    {
      title: '出发时间',
      key: 'goDate',
    },
    {
      title: '返程时间',
      key: 'backDate',
    },
    {
      title: '补贴金额',
      key: 'allowance',
    },
    {
      title: '操作',
      key: 'render',
      render: (rowData) => {
        return (
          <Button
            fill='none'
            type='info'
            onClick={() => {
              setVisible(true);
              setInitRecord(rowData);
            }}
            style={{ fontSize: '12px', display: 'flex' }}
          >
            详情
          </Button>
        );
      },
      fixed: 'right',
      width: 60,
      align: 'center',
    },
  ];
  const obj = {
    ...initRecord,
    projectType: ProjectTypes?.find((item) => item.key === initRecord?.projectType)?.title,
  };
  const types =
    initRecord?.projectType === 'SH' ? { workNumber: '工单编号', ...columnsType } : columnsType;

  return (
    <>
      <View className='reimbursement-detail_each'>
        <Text className='reimbursement-detail_each_left'>差旅信息明细</Text>
      </View>
      <Table
        columns={columns}
        data={travelInfo || []}
        striped
        style={{
          fontSize: '12px',
          maxWidth: '100%',
          boxSizing: 'border-box',
        }}
        bordered
      />
      <Popup
        visible={visible}
        position='right'
        destroyOnClose
        onClose={() => {
          setVisible(false);
        }}
        className='popup'
        title='详情'
      >
        <View className='popup-content' style={{ marginTop: navHeight }}>
          <Text className='popup-content_title'>详情</Text>
          {Object.keys(types).map((key) => {
            return (
              <View key={key} className='popup-detail_each'>
                <Text className='popup-detail_each_left'>{types[key] || ''}</Text>
                <Ellipsis
                  content={obj[key] || ''}
                  expandText='展开'
                  collapseText='收起'
                  direction='end'
                  className='popup-detail_each_right'
                />
              </View>
            );
          })}
        </View>
      </Popup>
    </>
  );
};

export default TravelDetail;
