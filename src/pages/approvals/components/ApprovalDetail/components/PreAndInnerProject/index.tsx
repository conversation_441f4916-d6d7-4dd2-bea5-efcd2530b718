import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { commonProjectPropsKeys } from '../PropsKeys';

import '../index.scss';

export const PreAndInnerProject = ({ info }) => {
  const { proBaseInfo } = info.fromData;
  const data = {
    ...proBaseInfo,
    projectType: proBaseInfo?.projectType === 'COOP' ? '合作' : '自营',
    status: proBaseInfo?.status === 'NORMAL' ? '正常' : '关闭',
  };
  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(commonProjectPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{commonProjectPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
