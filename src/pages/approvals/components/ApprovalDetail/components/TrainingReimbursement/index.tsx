import { Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

import { trainingPropsKeys } from '../PropsKeys';

import '../index.scss';

const ReturnStatus = { SUBMIT: '已提交', NOT_SUBMIT: '未提交', NULL: '无' };
const InvoiceStatus = { EXECUTING: '进行中', CLOSED: '已结束', TIME_OUT: '停止返还' };

export const TrainingReimbursement = ({ info }) => {
  const { operateState, state } = info?.fromData;
  const data = {
    ...info?.fromData,
    operateState: ReturnStatus[operateState],
    state: InvoiceStatus[state],
  };
  return (
    <Cell
      className='common'
      title={<View className='common-title'>审批详情</View>}
      description={
        <View className='common-detail'>
          {Object.keys(trainingPropsKeys).map((key) => {
            return (
              <View className='common-detail_each' key={key}>
                <View className='common-detail_each_left'>{trainingPropsKeys[key]}</View>
                <View className='common-detail_each_right'>{data[key] || ''}</View>
              </View>
            );
          })}
        </View>
      }
    ></Cell>
  );
};
