.approvals {
  --nutui-tabs-titles-background-color: '#fff';
  --nutui-tabs-tab-line-width: 100%;
  --nutui-tabs-titles-height: 56px;
  --nutui-tabs-titles-padding: 0 10px 0 16px;
  --nutui-grid-border-color: none;
  --nutui-grid-item-content-padding: 12px 10px;

  &-filter {
    width: 100%;
    display: flex;
    border-bottom: 1px solid #f5f5f5;
    height: 40px;
    align-items: center;
    overflow: hidden;

    &_tabs {
      width: 90%;
    }

    &_btn {
      width: 10%;
    }
  }

  &-list {
    padding: 5px 20px;
    background: var(--container-background-color);
  }

  &-popup {
    width: 70%;
    height: 100%;

    &_content {
      padding: 10px;

      &_title {
        font-size: 16px;
        font-weight: 600;
        padding-left: 10px;
      }

      &_btn {
        background: #f0f0f0;
        border-radius: 10px;
        box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
        width: 100%;
        text-align: center;
        padding: 10px 3px;
        font-size: 12px;
      }
    }
  }

  .tabpane {
    padding: 5px 20px;
  }

  .title {
    display: flex;

    .title-text {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &_bottom {
        font-size: 12px;
        color: grey;
      }
    }
  }
}
