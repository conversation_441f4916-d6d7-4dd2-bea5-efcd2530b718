import { useRef, useState } from 'react';
import { Button, Input } from '@nutui/nutui-react-taro';
import { ScrollView, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useReactive } from 'ahooks';

import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight } from '@/utils';
import { chatCompletion, ChatMessage } from '@/utils/ai';

import './index.scss';

interface Message {
  id: number;
  content: string;
  isSelf: boolean;
  timestamp: number;
}

interface StateType {
  list: API.WorkOrderPageResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo: API.WorkOrderPageResp;
  messages: Message[];
}

const listHeight = calcAvailableHeight(0, 44) + 'px';

const Ai = () => {
  const [inputText, setInputText] = useState('');
  const [scrollTop, setScrollTop] = useState(0);
  const [isSending, setIsSending] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const messagesRef = useRef<HTMLDivElement>(null);

  const navHeight = useSafeStatusbar();
  const $instance = useRouter().params;
  const type = $instance?.type || 'v3';

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: {},
    hasMore: true,
    messages: [
      {
        id: 1,
        content: '您好，我是智能助手，请问有什么可以帮您？',
        isSelf: false,
        timestamp: Date.now() - 1000,
      },
    ],
  });

  const handleSend = async () => {
    const userMessage: Message = {
      id: Date.now(),
      content: inputText.trim(),
      isSelf: true,
      timestamp: Date.now(),
    };

    try {
      setIsSending(true);
      state.messages.push(userMessage);
      setInputText('');
      setScrollTop((pre) => pre + 10000);

      // 构建消息历史，包含最新的用户消息
      const chatMessages: ChatMessage[] = state.messages.map((msg, index) => ({
        role: index === 0 ? 'system' : msg.isSelf ? 'user' : 'assistant',
        content: msg.content,
      }));

      // 创建新的AI回复消息对象
      const aiMessage: Message = {
        id: Date.now() + 1,
        content: '',
        isSelf: false,
        timestamp: Date.now(),
      };
      state.messages.push(aiMessage);

      // 调用AI接口
      await chatCompletion(chatMessages, type, (content: string) => {
        // 更新最后一条消息的内容
        const lastMessage = state.messages[state.messages.length - 1];
        if (!lastMessage.isSelf) {
          lastMessage.content += content;
          setScrollTop((pre) => pre + 10000);
        }
      });
    } catch (error) {
      Taro.showToast({ title: '消息发送失败，请重试!', icon: 'error' });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <View className='ai'>
      <TitleBar title={type === 'v3' ? '融智航云V3' : '融智航云R1'} />
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='ai-list'
      >
        <ScrollView
          scrollY
          scrollWithAnimation
          scrollTop={scrollTop}
          enhanced
          showScrollbar={false}
          className='messages'
          ref={messagesRef}
        >
          {state.messages.map((msg) => (
            <View key={msg.id} className={`message-item ${msg.isSelf ? 'self' : 'other'}`}>
              <View className='message-content'>{msg.content}</View>
            </View>
          ))}
        </ScrollView>
        <div className={`input ${isFocused ? 'focused' : ''}`}>
          <Input
            className='input-left'
            placeholder='发消息...'
            value={inputText}
            onChange={(val) => setInputText(val)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
          />
          <div className='input-right'>
            <Button
              type='primary'
              loading={isSending}
              disabled={!inputText.trim() || isSending}
              onClick={handleSend}
            >
              发送
            </Button>
          </div>
        </div>
      </View>
    </View>
  );
};

export default Ai;
