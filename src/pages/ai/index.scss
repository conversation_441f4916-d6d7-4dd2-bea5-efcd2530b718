.ai {
  height: 100vh;
  padding: 20px 0;
  background: #fff;

  .ai-list {
    position: relative;
    height: 100%;
    padding: 0 20px;
    overflow: hidden;
  }

  .input {
    position: fixed;
    right: 20px;
    bottom: 30px;
    left: 20px;
    z-index: 10; // 确保输入框在最上层
    display: flex;
    gap: 10px;
    align-items: center;
    height: 50px;
    padding: 0 25px 0 10px;
    background: #fff;
    border: 2px solid transparent;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:focus-within {
      border-color: var(--nutui-color-primary);
      box-shadow: 0 8px 24px rgba(82, 219, 199, 0.16), 0 2px 8px rgba(82, 219, 199, 0.24);
    }

    &.focused {
      border-color: var(--nutui-color-primary);
      box-shadow: 0 8px 24px rgba(82, 219, 199, 0.16), 0 2px 8px rgba(82, 219, 199, 0.24);
    }

    &-left {
      flex: 0.8;
    }
    &-right {
      display: flex;
      flex: 0.2;
      align-items: center;
    }
  }

  .messages {
    width: 100%;
    height: calc(100% - 60px);
  }

  .message-item {
    display: flex;
    width: 100%;
    margin: 10px 0;

    &.self {
      justify-content: flex-end;

      .message-content {
        color: #fff !important;
        background-color: var(--nutui-color-primary);
        border-radius: 16px 16px 0 16px;
      }
    }

    &.other {
      justify-content: flex-start;

      .message-content {
        color: #333 !important;
        background-color: #f5f5f5;
        border-radius: 16px 16px 16px 0;
      }
    }

    .message-content {
      max-width: 70%;
      padding: 10px 15px;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}
