import { useEffect, useState } from 'react';
import { ArrowDown, ArrowRight, VolumeMax } from '@nutui/icons-react-taro';
import { Collapse, Grid, Image, NoticeBar, Space } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useRequest } from 'ahooks';

import { announcementHomePage } from '@/api/announcement';
import IconFont from '@/components/IconFont/index';
import { useLoginStore } from '@/store';
import { calcAvailableHeight } from '@/utils';

import './index.scss';

const listHeight = calcAvailableHeight(70, 44) + 'px';

const messages = [
  '愿你在新的一周里充满活力和动力 💪😃',
  '今天要加油哦，别忘了给自己一个微笑 😊',
  '愿你心情愉快，工作顺利！😃👌',
  '坚持就是胜利，加油！💪',
  '一天的努力，一定会有所收获的！💪🌱',
  '周末快到了，继续加油！🎉💪',
  '今天是个休息的好日子，放松一下吧！🌞☕️',
];
var days = ['日', '一', '二', '三', '四', '五', '六'];

const templateIds = {
  approval: [
    'wx0PLw6QuF1eXlN00_FzhNrsMNPT4BBAFe-VUG4jIDQ',
    'kAG3GWHgALqp4MZdYpHUKpRXwvIWlPkBwODrZtxjMmc',
  ],
  claim: ['7enF5PMntBxvWaLCdMzUbkpRqynTHlfKcQa-oSkZXyo'],
  // 任务模板
  task: [
    'y7e6nJsE97t2b6JpblkXvQWEYI1h_PbmCGp25kAWfIQ', // 任务通知
    'VoZwV6_D7fTtZ4201jqVilwD8zOIHFlTu4eF0z1h5Js', // 任务延期通知
    'Kh9tgu7RX5mVripX4uz6yqb2CnNg4tuZ8SfdHIYq1Hs', // 任务关闭通知
  ],
  announcement: ['qpFEdGKpm3NgfH6jrUJO1a6jwEUx1jtgDf7XJDStM0A'],

  default: ['Sr5iVpXzBMMWPCd_OSng3_XGVgF21x0qd3KUV-f9KaQ'],
};

const Workbench = () => {
  const [notifyList, setNotifyList] = useState<string[]>([]);
  const userInfo = useLoginStore((state) => state.userInfo);
  const approvalCount = useLoginStore((state) => state.approvalCount);

  const dayOfWeek = new Date().getDay();
  const todayMessage = messages[dayOfWeek];
  days[dayOfWeek];

  const clickWorkBench = (type = 'approval') => {
    let temp: string[];

    switch (type) {
      case 'approval':
        temp = templateIds.approval;
        break;
      case 'claim':
        temp = templateIds.claim;
        break;
      case 'task':
        temp = templateIds.task;
        break;
      case 'announcement':
        temp = templateIds.announcement;
        break;

      default:
        temp = templateIds.default;
    }

    try {
      Taro.requestSubscribeMessage({
        tmplIds: temp,
        entityIds: [],
      });
    } catch (error) {
      console.error('请求订阅消息失败:', error);
    }
  };

  useRequest(
    () =>
      announcementHomePage({
        pageNum: 1,
        pageSize: 3,
      }),
    {
      ready: !!userInfo,
      onSuccess: (res: API.ResultPageAnnouncementPageResp) => {
        let strList: string[] = [];
        if (res?.data?.records?.length) {
          strList = res?.data?.records.map((i) => i.title as string);
        }
        setNotifyList([...strList]);
      },
    },
  );

  useEffect(() => {
    Taro.getSetting({
      withSubscriptions: true,
      success(res) {
        if (
          res.subscriptionsSetting?.['y7e6nJsE97t2b6JpblkXvQWEYI1h_PbmCGp25kAWfIQ'] !== 'accept' ||
          res.subscriptionsSetting?.['VoZwV6_D7fTtZ4201jqVilwD8zOIHFlTu4eF0z1h5Js'] !== 'accept' ||
          res.subscriptionsSetting?.['Kh9tgu7RX5mVripX4uz6yqb2CnNg4tuZ8SfdHIYq1Hs'] !== 'accept' ||
          res.subscriptionsSetting?.['qpFEdGKpm3NgfH6jrUJO1a6jwEUx1jtgDf7XJDStM0A'] !== 'accept'
        ) {
          Taro.showModal({
            title: '订阅提示',
            content: '为了保证任务通知正常，请订阅通知提醒',
            confirmText: '确定',
            cancelText: '取消',
            success: function (res) {
              if (res.confirm) {
                clickWorkBench('task');
                clickWorkBench('announcement');
                Taro.openSetting({
                  withSubscriptions: true,
                });
              }
            },
          });
        } else {
          Taro.showModal({
            title: `周${days[dayOfWeek]}快乐`,
            content: todayMessage,
            confirmText: '好的',
            showCancel: false,
            success: function (res) {
              if (res.confirm) {
                clickWorkBench('task');
                clickWorkBench('announcement');
              }
            },
          });
        }
      },
    });
  }, []);

  const oneNotify = notifyList.length === 1;

  return (
    <Space className='workbench' direction='vertical' style={{ height: listHeight }}>
      <Image src='https://oam.rklink.cn/banner.png' mode='aspectFill' height={200} />
      {notifyList?.length ? (
        <NoticeBar
          direction={oneNotify ? 'horizontal' : 'vertical'}
          speed={10}
          duration={3000}
          list={oneNotify ? undefined : notifyList}
          content={oneNotify ? notifyList[0] : undefined}
          leftIcon={<VolumeMax size='17' />}
          rightIcon={<ArrowRight />}
          onClick={() => {
            clickWorkBench('announcement');
            Taro.navigateTo({ url: '/pages/hr/Announcement/index' });
          }}
        ></NoticeBar>
      ) : null}
      {userInfo && (
        <View className='workbench-collapse'>
          <Collapse defaultActiveName={['1', '2', '3', '4']} expandIcon={<ArrowDown size='16' />}>
            <Collapse.Item title='常用' name='1'>
              <Grid>
                <Grid.Item
                  text='请假'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/hr/LeaveApplication/index' });
                  }}
                >
                  <IconFont name='leave' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='审批'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/approvals/index' });
                  }}
                >
                  {approvalCount && approvalCount > 0 ? (
                    <View className='workbench-count'>{approvalCount}</View>
                  ) : (
                    <></>
                  )}
                  <IconFont name='approval' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='开票信息'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/hr/BillingInformation/index' });
                  }}
                >
                  <IconFont name='author' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='工单'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/workorder/index' });
                  }}
                >
                  <IconFont name='statistics1' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='周报'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/weekly/index' });
                  }}
                >
                  <IconFont name='weekly' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='融智航云V3'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/ai/index?type=v3' });
                  }}
                >
                  <IconFont name='customer' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='融智航云R1'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/ai/index?type=r1' });
                  }}
                >
                  <IconFont name='customer' size={45} />
                </Grid.Item>
              </Grid>
            </Collapse.Item>
            <Collapse.Item title='人事' name='2'>
              <Grid>
                <Grid.Item
                  text='通讯录'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/hr/AddressBook/index' });
                  }}
                >
                  <IconFont name='schedule' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='请假'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/hr/LeaveApplication/index' });
                  }}
                >
                  <IconFont name='leave' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='销假'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/hr/LeaveSell/index' });
                  }}
                >
                  <IconFont name='leave_sell' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='公告'
                  onClick={() => {
                    clickWorkBench('announcement');
                    Taro.navigateTo({ url: '/pages/hr/Announcement/index' });
                  }}
                >
                  <IconFont name='tousujianyibeifen' size={45} />
                </Grid.Item>
              </Grid>
            </Collapse.Item>
            <Collapse.Item title='财务' name='3'>
              <Grid>
                <Grid.Item
                  text='项目付款'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/finance/GeneralPayment/index' });
                  }}
                >
                  <IconFont name='payment' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='收款记录'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/finance/Receipt/index' });
                  }}
                >
                  <IconFont name='collection' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='认领'
                  onClick={() => {
                    clickWorkBench('claim');
                    Taro.navigateTo({ url: '/pages/finance/Claim/index' });
                  }}
                >
                  <IconFont name='claim' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='待付款记录'
                  onClick={() => {
                    clickWorkBench('out');
                    Taro.navigateTo({ url: '/pages/finance/OutStandingPayment/index' });
                  }}
                >
                  <IconFont name='statistics1' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='付款记录'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/finance/PaymentStatement/index' });
                  }}
                >
                  <IconFont name='submitted' size={45} />
                </Grid.Item>
              </Grid>
            </Collapse.Item>
            <Collapse.Item title='合同' name='4'>
              <Grid>
                <Grid.Item
                  text='开票申请'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/contract/ApplyInvoice/index' });
                  }}
                >
                  <IconFont name='claimed' size={45} />
                </Grid.Item>
                <Grid.Item
                  text='付款申请'
                  onClick={() => {
                    clickWorkBench();
                    Taro.navigateTo({ url: '/pages/contract/ApplyPayment/index' });
                  }}
                >
                  <IconFont name='apply_payment' size={45} />
                </Grid.Item>
              </Grid>
            </Collapse.Item>
          </Collapse>
        </View>
      )}
    </Space>
  );
};

export default Workbench;
