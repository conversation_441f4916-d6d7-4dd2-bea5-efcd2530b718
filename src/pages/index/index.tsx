import { useEffect } from 'react';
import { Apps, User } from '@nutui/icons-react-taro';
import { Avatar, Button, NavBar, Tabbar } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

import { currentInfo, wxLogin } from '@/api/auth';
import { useSafeStatusbar } from '@/hooks';
import { useLoginStore } from '@/store';
import { getGradientColorFromCharCode } from '@/utils';

import Workbench from './components/workbench/index';

import './index.scss';

const App = () => {
  const navHeight = useSafeStatusbar();
  const store = useLoginStore();

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = () => {
    try {
      const token = Taro.getStorageSync('RKLINK_OA_TOKEN');
      if (!token) {
        store.setUserInfo(null);
      }
    } catch (e) {
      store.setUserInfo(null);
    }
  };

  // 处理wxLogin或wxRefToken接口的返回逻辑
  const handleLogRes = async (res) => {
    if (res?.data?.authorization) {
      Taro.setStorageSync('RKLINK_OA_TOKEN', res.data.authorization);
      Taro.setStorageSync('RKLINK_OA_REFRESH_TOKEN', res.data.refreshAuthorization);
      const userInfo = await currentInfo({});
      if (userInfo?.data) {
        store.setUserInfo(userInfo.data);
        Taro.showToast({ title: '登录成功', icon: 'success' });
      }
    }
  };

  const onGetNumber = (e) => {
    if (e?.detail?.code) {
      Taro.login({
        success: async (res) => {
          store.setNeedPhone(false);
          console.warn('Taro.login方法获取的回调', res);
          if (res.code) {
            // 发送 res.code 到后端换取 openId, sessionKey 等数据
            const loginRes = await wxLogin({
              code: res.code,
              phoneCode: e?.detail?.code,
            });
            console.warn('wx-login接口获取的回调', loginRes);
            if (loginRes.code === 200) {
              await handleLogRes(loginRes);
            }
          } else {
            console.error('登录失败！', res.errMsg);
          }
        },
        fail: (err) => {
          console.error('登录失败', err);
        },
      });
    }
  };

  return (
    <View className='home'>
      <NavBar
        titleAlign='left'
        onBackClick={() => {}}
        back={
          <>
            {!store.userInfo && <Avatar icon={<User />} />}
            {store.userInfo && store.userInfo.username && (
              <Avatar
                size='small'
                color='#fff'
                style={getGradientColorFromCharCode(store.userInfo.username?.charCodeAt(1))}
              >
                {store.userInfo.username.slice(-2)}
              </Avatar>
            )}
          </>
        }
        style={{ marginTop: navHeight }}
      >
        {store?.userInfo ? (
          '工作台'
        ) : store.needPhone ? (
          <Button
            fill='none'
            className='login-button'
            openType='getPhoneNumber'
            onGetPhoneNumber={onGetNumber}
          >
            点击绑定手机号
          </Button>
        ) : (
          '正在登录中...'
        )}
      </NavBar>
      <View className='content'>
        <Workbench />
      </View>
      <Tabbar fixed defaultValue={0}>
        <Tabbar.Item title='工作台' icon={<Apps size={22} />} />
      </Tabbar>
    </View>
  );
};

export default App;
