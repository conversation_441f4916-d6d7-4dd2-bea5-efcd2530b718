import { useEffect } from 'react';
import { Cell, InfiniteLoading, Skeleton, Tag } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';

import { weekPage } from '@/api/week';
import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';
import { useLoginStore } from '@/store';
import { calcAvailableHeight } from '@/utils';

// import PaymentDrawer from './components/PaymentDrawer';
import './index.scss';

interface StateType {
  list: API.WeekPageResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo: API.WeekPageResp;
}

const listHeight = calcAvailableHeight(0, 44) + 'px';

const Weekly = () => {
  const userInfo = useLoginStore((state) => state.userInfo);
  const navHeight = useSafeStatusbar();

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: {},
    hasMore: true,
  });
  const pagination = useReactive({
    pageNum: 1,
    pageSize: 15,
  });

  const { run: getList, loading } = useRequest(
    () =>
      weekPage({
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        search: {
          employeeId: userInfo?.id || undefined,
          status: 'DRAFT',
        },
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.data?.records && res?.data?.records?.length > 0) {
          if (pagination.pageNum === 1) {
            state.list = res.data?.records;
          } else {
            state.list = [...state.list, ...res?.data?.records];
          }
        } else {
          if (pagination.pageNum === 1) state.list = [];
          state.hasMore = false;
        }
      },
    },
  );

  Taro.useDidShow(() => {
    backHandleLists();
  });

  useEffect(() => {
    getList();
  }, []);

  const loadMore = async () => {
    pagination.pageNum += 1;
    getList();
  };

  const refresh = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  //从详情页返回时请求
  const backHandleLists = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  return (
    <View className='weekly'>
      <TitleBar title='周报列表' />
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='weekly-list'
      >
        {loading && pagination.pageNum === 1 ? (
          <Skeleton rows={20} animated />
        ) : (
          <ul id='scrollDemo' className='weekly-ul'>
            <InfiniteLoading
              pullingText='松开刷新'
              loadingText='加载中'
              loadMoreText='没有更多了'
              pullRefresh
              target='scrollDemo'
              hasMore={state.hasMore}
              onLoadMore={loadMore}
              onRefresh={refresh}
            >
              {state.list.map((record: API.WeekPageResp) => {
                return (
                  <View
                    className='weekly-cell_each'
                    key={record.id}
                    onClick={() => {
                      Taro.navigateTo({
                        url: `/pages/weekly/components/WeeklyForm/index?id=${record.id}`,
                      });
                    }}
                  >
                    <Cell.Group divider={false}>
                      <Cell
                        className='weekly-cell_top'
                        title={
                          <View className='weekly-cell_title'>
                            <View>{record.documentNumber || '暂无编号'}</View>
                          </View>
                        }
                        extra={
                          <Tag className='weekly-cell_tag' type='warning'>
                            {record.status === 'DRAFT' ? '草稿' : '已提交'}
                          </Tag>
                        }
                      />
                      <Cell>
                        <View className='weekly-cell-desc'>
                          <View className='weekly-cell_line'>
                            <View className='weekly-cell_line_left'>汇总人：</View>
                            <View className='weekly-cell_line_right'>
                              {record.employeeName || '暂无数据'}
                            </View>
                          </View>
                          <View className='weekly-cell_line'>
                            <View className='weekly-cell_line_left'>开始日期：</View>
                            <View className='weekly-cell_line_right'>
                              {record.startTime || '暂无数据'}
                            </View>
                          </View>
                          <View className='weekly-cell_line'>
                            <View className='weekly-cell_line_left'>结束日期：</View>
                            <View className='weekly-cell_line_right'>
                              {record.endTime || '暂无数据'}
                            </View>
                          </View>
                          <View className='weekly-cell_line'>
                            <View className='weekly-cell_line_left'>总工时：</View>
                            <View className='weekly-cell_line_right'>
                              {record.countTime || '-'}
                            </View>
                          </View>
                        </View>
                      </Cell>
                    </Cell.Group>
                  </View>
                );
              })}
            </InfiniteLoading>
          </ul>
        )}
      </View>
      {/*<PaymentDrawer*/}
      {/*  showRight={state.drawerVisible}*/}
      {/*  currentInfo={state.currentInfo}*/}
      {/*  closeDrawer={closeDrawer}*/}
      {/*  refresh={refresh}*/}
      {/*/>*/}
    </View>
  );
};

export default Weekly;
