import { Button, Cell, Dialog, Input, InputNumber, Notify, Picker, Space } from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { updateWeek, weekInfo, weekStatus } from '@/api/week';
import SearchSelect, { SearchItemProps } from '@/components/SearchSelect';
import TitleBar from '@/components/TitleBar';
import { WORK_TYPE } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { useAvailableProjectList } from '@/hooks/useAvailableProjectList';
import { calcAvailableBottom, isValueNotEmpty } from '@/utils';

import './index.scss';

dayjs.locale('zh-cn');

const workTypeList = WORK_TYPE.map((i) => ({ value: i.value, text: i.label }));
const aBottom = calcAvailableBottom();
const bottomHeight = isValueNotEmpty(aBottom) ? (aBottom as number) : 0;

//为防止dayjs本地化
const enToCn = (week: string) => {
  const formattedWeek = week?.toLowerCase() || week;
  switch (formattedWeek) {
    case 'monday':
      return '星期一';
    case 'tuesday':
      return '星期二';
    case 'wednesday':
      return '星期三';
    case 'thursday':
      return '星期四';
    case 'friday':
      return '星期五';
    case 'saturday':
      return '星期六';
    case 'sunday':
      return '星期日';
  }
  return week;
};

const WeeklyForm = () => {
  const $instance = useRouter().params;

  const navHeight = useSafeStatusbar();
  const { availableProjectList, loading: availableProjectLoading } = useAvailableProjectList();

  //表单项
  const globalState = useReactive<{ form: API.WeekInfoResp; getCountTime: number }>({
    form: {
      countTime: 0,
      employeeName: '',
      endTime: '',
      id: '',
      nextWeeks: [],
      startTime: '',
      weekDetailsList: [],
    },
    get getCountTime() {
      return this.form.weekDetailsList.reduce((pre, cur) => {
        return pre + cur.workTime;
      }, 0);
    },
  });
  //弹窗项
  const visibleState = useReactive({
    notifyVisible: false,
    notifyMassage: '请完善必填项',
    submitVisible: true, //提交后不允许再编辑
  });

  useRequest(() => weekInfo({ req: { id: $instance.id as string } }), {
    onSuccess: (res) => {
      if (res?.code === 200) {
        Object.keys(globalState.form).forEach((key) => {
          if (key === 'weekDetailsList') {
            globalState.form['weekDetailsList'] =
              Array.isArray(res?.data?.weekDetailsList) && res.data.weekDetailsList?.length
                ? res.data.weekDetailsList.map((i) => ({
                  ...i,
                  isProjectOpen: false,
                  isWorkOpen: false,
                  recordWeek: i.recordDate ? enToCn(dayjs(i.recordDate).format('dddd')) : '',
                }))
                : [];
          } else {
            globalState.form[key] = res?.data?.[key];
          }
        });
      }
    },
  });

  const { run: changeStatus, loading: changeStatusLoading } = useRequest(
    () =>
      weekStatus({
        id: $instance.id,
        status: 'SUBMITTED',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '提交成功', icon: 'success' });
          //隐藏所有提交按钮
          visibleState.submitVisible = false;
        } else if (res?.code === 500 && res?.message) {
          visibleState.notifyMassage = res.message;
          visibleState.notifyVisible = true;
        }
      },
    },
  );

  //仅保存
  const { run: updateForm, loading: submitLoading } = useRequest((value) => updateWeek(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        Taro.showToast({ title: '保存成功', icon: 'success' });
      }
    },
  });
  //保存并提交
  const { run: updateFormAndSubmit } = useRequest((value) => updateWeek(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        changeStatus();
      }
    },
  });

  return (
    <View className='weekly'>
      <TitleBar title={`已填工时：${globalState.getCountTime || 0}`} />
      <View
        className='weekly-container'
        style={{ marginTop: navHeight + 20, marginBottom: bottomHeight + 70 + 'px' }}
      >
        {globalState.form.weekDetailsList?.map(
          (
            week: API.WeekDetailsInfoResp & { isProjectOpen: boolean; isWorkOpen: boolean },
            key,
          ) => {
            return (
              <Cell.Group divider={false} key={key}>
                <Cell
                  title={
                    <View className='weekly-container_title'>
                      <Text>
                        {week.recordDate}({week.recordWeek ?? '星期一'})
                      </Text>
                    </View>
                  }
                />
                <Cell
                  title={<View className='weekly-container_label'>工作记录</View>}
                  extra={
                    <View className='weekly-container_form'>
                      <Input
                        value={week.content}
                        onChange={(val) => (week.content = val)}
                        placeholder='请输入工作记录'
                      />
                    </View>
                  }
                />
                <Cell
                  title={
                    <View className='weekly-container_label'>
                      {availableProjectLoading ? '数据准备中' : '所属项目'}
                    </View>
                  }
                  extra={
                    <View className='weekly-container_form'>
                      <Cell description={week.projectNumber ? week.projectName : '点击选择'} />
                      {availableProjectList?.length && <SearchSelect
                        key={`project_${key}`}
                        originList={availableProjectList as SearchItemProps[]}
                        visible={week.isProjectOpen}
                        close={() => {
                          week.isProjectOpen = false;
                        }}
                        done={(options: PickerOption[] & API.ProBaseInfoResp[]) => {
                          if (options?.length) {
                            week.projectId = options[0].id;
                            week.projectName = options[0].projectName;
                            week.projectNumber = options[0].projectNumber;
                          }
                        }}
                      />
                      }
                    </View>
                  }
                  onClick={() => {
                    if (availableProjectLoading) {
                      return;
                    }
                    week.isProjectOpen = true;
                  }}
                />
                <Cell
                  title={<View className='weekly-container_label'>工作类型</View>}
                  extra={
                    <View className='weekly-container_form'>
                      <Cell description={week.workType ?? '点击选择'} />
                      <Picker
                        title='请选择项目'
                        visible={week.isWorkOpen}
                        options={workTypeList}
                        onConfirm={(options) => {
                          if (options?.length) {
                            week.workType = options[0].value as string;
                          }
                        }}
                        onClose={() => (week.isWorkOpen = false)}
                      />
                    </View>
                  }
                  onClick={() => {
                    if (availableProjectLoading) {
                      return;
                    }
                    week.isWorkOpen = true;
                  }}
                />
                <Cell
                  title={<View className='weekly-container_label'>工作时间</View>}
                  extra={
                    <View className='weekly-container_form'>
                      <InputNumber
                        value={week.workTime}
                        min={1}
                        max={24}
                        step={1}
                        onChange={(val: number) => {
                          if (val > 24) {
                            week.workTime = 24;
                          } else if (val < 1) {
                            week.workTime = 1;
                          } else {
                            week.workTime = Number(val);
                          }
                        }}
                      />
                    </View>
                  }
                />
                <Cell
                  align='center'
                  extra={
                    <Space>
                      <Button
                        type='danger'
                        size='mini'
                        disabled={week.ifDefault === 0}
                        onClick={() => {
                          globalState.form.weekDetailsList?.splice(key, 1);
                        }}
                      >
                        删除
                      </Button>
                      <Button
                        type='primary'
                        size='mini'
                        onClick={() => {
                          const newWeek = { ...week, ifDefault: 1 };
                          globalState.form.weekDetailsList?.splice(key + 1, 0, newWeek);
                        }}
                      >
                        复制此行
                      </Button>
                    </Space>
                  }
                />
              </Cell.Group>
            );
          },
        )}
      </View>
      {visibleState.submitVisible && (
        <View className='detail-main_actions' style={{ height: 30 + bottomHeight + 'px' }}>
          <Button
            loading={submitLoading || changeStatusLoading}
            onClick={() => {
              const form = {
                ...globalState.form,
                countTime: globalState.getCountTime,
              };
              updateForm(form);
            }}
            shape='square'
            fill='none'
            className='detail-main_actions_button'
          >
            保存
          </Button>
          <View className='detail-main_actions_divider' />
          <Button
            onClick={async () => {
              const form = {
                ...globalState.form,
                countTime: globalState.getCountTime,
              };
              if (globalState.getCountTime < 40) {
                Dialog.open('weekTime', {
                  title: '确认工时',
                  content: '未达到应上班公时，请确认是否提交？',
                  onConfirm: async () => {
                    updateFormAndSubmit(form);
                    Dialog.close('weekTime');
                  },
                  onCancel: () => {
                    Dialog.close('weekTime');
                  },
                });
              } else {
                updateFormAndSubmit(form);
              }
            }}
            loading={submitLoading || changeStatusLoading}
            shape='square'
            fill='none'
            className='detail-main_actions_button'
          >
            保存并提交
          </Button>
        </View>
      )}
      <Dialog id='weekTime' />
      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleState.notifyVisible}
        type='danger'
        onClose={() => {
          visibleState.notifyVisible = false;
        }}
      >
        {visibleState.notifyMassage}
      </Notify>
    </View>
  );
};

export default WeeklyForm;
