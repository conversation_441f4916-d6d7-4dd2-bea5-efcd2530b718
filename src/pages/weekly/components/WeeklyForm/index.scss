.weekly {
  --nutui-form-item-error-line-color: var(--error-color);
  --nutui-form-item-required-color: var(--error-color);
  --nutui-form-item-error-message-color: var(--error-color);
  padding: 15px;
  background: var(--container-background-color);

  &-container {
    width: 100%;

    &_title {
      display: flex;
      align-items: flex-end;
      font-weight: 500;
    }
    &_label {
      display: flex;
      align-items: center;
      height: 35px;
    }
    &_form {
      display: flex;
      align-items: center;
      height: 35px;
    }
  }

  .detail-main_actions {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    width: 100%;
    padding: 10px 0;
    background: #fff;
    box-shadow: var(--nutui-cell-box-shadow, 0px 1px 7px 0px rgb(237, 238, 241));

    &_button {
      flex: 1;
      color: var(--primary-color);
      font-size: 14px;
    }
    &_divider {
      position: relative;
      top: 5px;
      width: 1px;
      height: 20px;
      background: #e5e5e5;
    }
  }
}
