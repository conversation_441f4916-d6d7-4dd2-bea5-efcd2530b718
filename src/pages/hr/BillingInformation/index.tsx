import { useEffect, useState } from 'react';
import { Agenda } from '@nutui/icons-react-taro';
import { Cell, Divider } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

import { billingInformationData } from '@/api/dictionary';
import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';

import './index.scss';

const BillingInformation = () => {
  const navHeight = useSafeStatusbar();
  const [billingInfo, setBillingInfo] = useState<API.BillingInformationDTO>({});

  useEffect(() => {
    const fetchBillingInfo = async () => {
      try {
        const response = await billingInformationData();
        if (response.code === 200 && response.data) {
          setBillingInfo(response.data);
        }
      } catch (error) {
        console.error('获取开票信息失败:', error);
      }
    };

    fetchBillingInfo();
  }, []);

  const infoList = [
    { title: '更新日期', value: billingInfo.updateDate || '' },
    { title: '公司邮寄地址', value: billingInfo.mailingAddress || '' },
    { title: '发票抬头', value: billingInfo.invoiceTitle || '' },
    { title: '纳税人识别号', value: billingInfo.taxpayerId || '' },
    { title: '注册地址', value: billingInfo.registeredAddress || '' },
    { title: '电话', value: billingInfo.phoneNumber || '' },
    { title: '开户名(单位名称)', value: billingInfo.accountName || '' },
    { title: '开户行', value: billingInfo.bankName || '' },
    { title: '账号', value: billingInfo.bankAccount || '' },
  ];

  return (
    <View className='billing'>
      <TitleBar title='发票信息' />
      <View className='billing-container' style={{ top: navHeight + 30 }}>
        <Cell.Group className='billing-container_group' divider={false}>
          {infoList.map((item, index) => {
            return (
              <Cell
                key={index}
                title={<View className='billing-container_group_label'>{item.title}</View>}
                extra={
                  <View className='billing-container_group_value'>
                    <View>{item.value}</View>
                    <View>
                      <Divider direction='vertical' className='billing-container_group_divider' />
                    </View>
                    <View
                      onClick={() => {
                        Taro.setClipboardData({
                          data: item.value,
                        });
                      }}
                    >
                      <Agenda size={16} className='billing-container_group_icon' />
                    </View>
                  </View>
                }
              />
            );
          })}
        </Cell.Group>
      </View>
    </View>
  );
};

export default BillingInformation;
