.billing {
  &-container {
    padding: 20px;
    background: var(--container-background-color);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-y: auto;

    &_group {
      border-radius: 6px;
      box-shadow: var(--nutui-cell-box-shadow, 0px 1px 7px 0px rgb(237, 238, 241));

      .nut-cell-extra {
        flex: 2;
      }

      &_label {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }

      &_value {
        font-size: 14px;
        font-weight: 500;
        text-align: right;
        display: flex;
      }

      &_icon {
        color: var(--primary-color);
        position: relative;
        top: 2px;
      }

      &_divider {
        height: 100%;
      }
    }
  }
}
