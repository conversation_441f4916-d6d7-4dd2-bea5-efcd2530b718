import { useEffect } from 'react';
import { Cell, InfiniteLoading, Skeleton } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';

import { announcementInfo, announcementPage } from '@/api/announcement';
import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight } from '@/utils';

import AnnouncementDrawer from './components/AnnouncementDrawer';

import './index.scss';

interface StateType {
  list: API.AnnouncementPageResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo: API.AnnouncementPageResp;
}

const listHeight = calcAvailableHeight(0, 44) + 'px';

const Announcement = () => {
  const navHeight = useSafeStatusbar();

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: {},
    hasMore: true,
  });
  const pagination = useReactive({
    pageNum: 1,
    pageSize: 15,
  });

  const { run: getList, loading } = useRequest(
    () => announcementPage({ pageNum: pagination.pageNum, pageSize: pagination.pageSize }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.data?.records && res?.data?.records?.length > 0) {
          if (pagination.pageNum === 1) {
            state.list = res.data?.records;
          } else {
            state.list = [...state.list, ...res?.data?.records];
          }
        } else {
          if (pagination.pageNum === 1) state.list = [];
          state.hasMore = false;
        }
      },
    },
  );
  // 获取详情
  const { run: getInfo } = useRequest((id) => announcementInfo({ id }), {
    manual: true,
    onSuccess: (res) => {
      state.currentInfo = res?.data || {};
      state.drawerVisible = true;
      Taro.hideLoading();
    },
  });

  useEffect(() => {
    getList();
  }, []);

  const loadMore = async () => {
    pagination.pageNum += 1;
    getList();
  };

  const refresh = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  const closeDrawer = () => {
    pagination.pageNum = 1;
    state.currentInfo = {};
    state.drawerVisible = false;
  };

  return (
    <View className='announcement'>
      <TitleBar title='公告列表' />
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='announcement-list'
      >
        {loading && pagination.pageNum === 1 ? (
          <Skeleton rows={20} animated />
        ) : (
          <ul id='scrollDemo' className='announcement-ul'>
            <InfiniteLoading
              pullingText='松开刷新'
              loadingText='加载中'
              loadMoreText='没有更多了'
              pullRefresh
              target='scrollDemo'
              hasMore={state.hasMore}
              onLoadMore={loadMore}
              onRefresh={refresh}
            >
              {state.list.map((record: API.AnnouncementPageResp) => {
                return (
                  <View
                    className='announcement-cell_each'
                    key={record.id}
                    onClick={() => {
                      Taro.showLoading({
                        title: '加载中',
                      });
                      getInfo(record.id);
                    }}
                  >
                    <Cell.Group divider={false}>
                      <Cell
                        className='announcement-cell_top'
                        title={
                          <View className='announcement-cell_title'>
                            {record.title || '暂无标题'}
                          </View>
                        }
                      />
                      <Cell>
                        <View className='announcement-cell-desc'>
                          <View className='announcement-cell_line'>
                            <View className='announcement-cell_line_left'>发布时间：</View>
                            <View className='announcement-cell_line_right'>
                              {record.releasedTime || '暂无数据'}
                            </View>
                          </View>
                        </View>
                      </Cell>
                    </Cell.Group>
                  </View>
                );
              })}
            </InfiniteLoading>
          </ul>
        )}
      </View>
      <AnnouncementDrawer
        currentInfo={state.currentInfo}
        showBottom={state.drawerVisible}
        closeDrawer={closeDrawer}
      />
    </View>
  );
};

export default Announcement;
