.announcement {
  height: 100vh;
  padding: 20px;
  background: var(--container-background-color);
  --nutui-skeleton-background: #e8e8e8;

  &-ul {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .announcement-cell_each {
    width: 98%;
    margin-left: 5px;
    background: #fff;
    border-radius: 10px;

    .announcement-cell_top {
      --nutui-cell-padding: 13px 16px 0 16px;
    }

    .announcement-cell_title {
      font-weight: bold;
      font-size: 14px;
      white-space: nowrap;
    }

    .announcement-cell-desc {
      width: 100%;

      .announcement-cell_line {
        display: flex;
        width: 100%;
        margin: 5px 0;

        &_left {
          width: 35%;
          margin-right: 8px;
          color: #999;
          white-space: nowrap;
        }

        &_right {
          width: 65%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          word-wrap: break-word;
        }
      }
    }
  }
}
