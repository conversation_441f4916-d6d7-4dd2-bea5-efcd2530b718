import { useCallback } from 'react';
import { Popup } from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';

import './index.scss';

interface DrawerProps {
  showBottom: boolean;
  closeDrawer: () => void;
  currentInfo: API.AnnouncementPageResp;
}

const AnnouncementDrawer = (props: DrawerProps) => {
  const { showBottom, closeDrawer, currentInfo } = props;

  const handleContentClick = useCallback(() => {
    // 获取所有图片URL
    const imgReg = /<img.*?src="(.*?)".*?>/g;
    const content = currentInfo?.content || '';
    const imgList: string[] = [];
    let match;

    while ((match = imgReg.exec(content)) !== null) {
      imgList.push(match[1]);
    }
    Taro.previewMedia({ sources: imgList?.map((i) => ({ url: i })) });
  }, [currentInfo?.content]);

  const formatContent = useCallback((content: string) => {
    return content.replace(/&nbsp;/g, ' ');
  }, []);

  return (
    <Popup
      destroyOnClose
      className='pop'
      visible={showBottom}
      position='bottom'
      title={currentInfo?.title || '公告内容'}
      onClose={() => {
        closeDrawer();
      }}
    >
      <ScrollView scrollY className='pop-container'>
        <div
          style={{ width: '90%' }}
          onClick={handleContentClick}
          dangerouslySetInnerHTML={{ __html: formatContent(currentInfo?.content || '') }}
        />
      </ScrollView>
    </Popup>
  );
};

export default AnnouncementDrawer;
