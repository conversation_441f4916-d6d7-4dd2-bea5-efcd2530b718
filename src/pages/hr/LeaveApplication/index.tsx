import { useEffect, useState } from 'react';
import { ArrowRight } from '@nutui/icons-react-taro';
import {
  Button,
  Calendar,
  Cell,
  Form,
  Input,
  Notify,
  Picker,
  TextArea,
} from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { createLeaveAppMobile } from '@/api/employee';
import { checkSectionHoliday } from '@/api/holiday';
import { leaveAppBalance } from '@/api/leaveApp';
import SearchSelect, { SearchItemProps } from '@/components/SearchSelect';
import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';
import { useDepartment } from '@/hooks/useDepartment';
import { useLoginStore } from '@/store';

import './index.scss';

const Leave = () => {
  const navHeight = useSafeStatusbar();
  const { userInfo } = useLoginStore();
  const { departmentList } = useDepartment();
  const [form] = Form.useForm();

  const [startDay, setStartDay] = useState<string>();
  const [endDay, setEndDay] = useState<string>();
  const [halfDay, setHalfDay] = useState<string>();
  const [leaveTypes, setLeaveTypes] = useState<string>();

  // 弹窗开关集合
  const visibleCollection = useReactive({
    fromDate: false,
    toDate: false,
    halfDate: false,
    leaveType: false,
    notifyVisible: false,
    notifyMassage: '请先选择开始日期和结束日期',
  });

  // 休假类型列表
  const { data: balanceData, loading: balanceLoading } = useRequest(leaveAppBalance);
  const leaveList = balanceData?.data?.filter(i => !['工伤假', '育儿假', '丧假'].includes(i.leaveTypeName || ''))?.map((i) => ({
    ...i,
    value: i.id as string,
    text: i.leaveTypeName as string,
  }));

  const { run: getTotalLeaveDays } = useRequest((value) => checkSectionHoliday(value), {
    manual: true,
    onSuccess: (data) => {
      form.setFieldsValue({ totalLeaveDays: data?.data });
      if (halfDay) {
        form.setFieldsValue({ totalLeaveDays: Number(data?.data) - 0.5 });
      }
    },
  });

  const { run: submitForm, loading: submitLoading } = useRequest(
    (value) => createLeaveAppMobile(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '提交成功', icon: 'success' });
          setTimeout(() => {
            Taro.navigateBack();
          }, 1800);
        }
      },
    },
  );

  const submitSucceed = (values: any) => {
    submitForm(values);
  };

  const submitFailed = () => {
    visibleCollection.notifyMassage = '请完善必填项！';
    visibleCollection.notifyVisible = true;
  };

  useEffect(() => {
    if (departmentList.length && userInfo) {
      const department = departmentList.find((i) => i.value === userInfo?.department);
      if (department) {
        form.setFieldsValue({
          department: department.value,
          employee: userInfo?.employeeNumber,
          employeeName: userInfo?.username,
        });
      }
    }
  }, [departmentList, form, userInfo]);

  useEffect(() => {
    if (startDay && endDay) {
      if (['年假', '育儿假', '事假']?.includes(leaveTypes!)) {
        getTotalLeaveDays({
          startHolidays: startDay,
          endHolidays: endDay,
        });
      } else {
        const days = dayjs(endDay).diff(startDay, 'day') + 1;
        form.setFieldsValue({ totalLeaveDays: days });
        if (halfDay) {
          //上半天时间不能小于开始时间,上半天日期不能大于结束时间
          if (new Date(halfDay) < new Date(startDay) || new Date(halfDay) > new Date(endDay)) {
            form.setFieldsValue({ halfDate: undefined });
          } else {
            form.setFieldsValue({ totalLeaveDays: Number(days) - 0.5 });
          }
        }
      }
    }
    if (!startDay || !endDay) {
      form.setFieldsValue({ halfDate: undefined });
    }
  }, [form, startDay, endDay, halfDay, leaveTypes]);

  return (
    <View className='leave'>
      <TitleBar title='请假' />
      <View className='leave-container' style={{ marginTop: navHeight + 20 }}>
        <Form
          form={form}
          className='leave-container_form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={submitFailed}
          footer={
            <Button
              loading={submitLoading}
              className='leave-container_form_footer'
              formType='submit'
              type='primary'
            >
              提交
            </Button>
          }
        >
          <Form.Item label='员工编号' name='employee'>
            <Input disabled placeholder='请输入员工编号' type='text' />
          </Form.Item>
          <Form.Item label='员工姓名' name='employeeName'>
            <Input disabled placeholder='请输入员工姓名' type='text' />
          </Form.Item>
          <Form.Item label='部门' name='department'>
            <Picker options={departmentList}>
              {(value: string) => {
                return (
                  <Cell
                    className='leave-container_form_cell'
                    title={departmentList.find((i) => i.value === value)?.text ?? '请选择'}
                    align='center'
                  />
                );
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            label='休假类型'
            name='leaveType'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  if (balanceLoading) {
                    return;
                  }
                  visibleCollection.leaveType = true;
                }}
                className='leave-container_form_cell'
                title={balanceLoading ? '数据准备中' : leaveTypes ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              {leaveList && <SearchSelect
                originList={leaveList as SearchItemProps[]}
                visible={visibleCollection.leaveType}
                close={() => {
                  visibleCollection.leaveType = false;
                }}
                done={(options: PickerOption[] & API.LeaveBalanceListResp[]) => {
                  if (options?.length) {
                    form.setFieldsValue({
                      leaveBalance: options[0]?.leaveBalance,
                      leaveType: options[0]?.value,
                    });
                    setLeaveTypes(options[0]?.text as string);
                  }
                }}
              />
              }
            </>
          </Form.Item>
          <Form.Item label='可用天数' name='leaveBalance'>
            <Input disabled placeholder='请输入可用天数' type='text' />
          </Form.Item>
          <Form.Item
            label='开始日期'
            name='fromDate'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.fromDate = true;
                }}
                className='leave-container_form_cell'
                title={form.getFieldValue('fromDate') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Calendar
                autoBackfill
                visible={visibleCollection.fromDate}
                onClose={() => (visibleCollection.fromDate = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  if (endDay && new Date(current) > new Date(endDay)) {
                    visibleCollection.notifyMassage = '开始日期不能大于结束日期';
                    visibleCollection.notifyVisible = true;
                    return;
                  }

                  form.setFieldsValue({
                    fromDate: current,
                  });
                  setStartDay(current);
                }}
              />
            </>
          </Form.Item>
          <Form.Item
            label='结束日期'
            name='toDate'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.toDate = true;
                }}
                className='leave-container_form_cell'
                title={form.getFieldValue('toDate') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Calendar
                autoBackfill
                visible={visibleCollection.toDate}
                onClose={() => (visibleCollection.toDate = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  if (startDay && new Date(current) < new Date(startDay)) {
                    visibleCollection.notifyMassage = '结束日期不能小于开始日期';
                    visibleCollection.notifyVisible = true;
                    return;
                  }

                  form.setFieldsValue({
                    toDate: current,
                  });
                  setEndDay(current);
                }}
              />
            </>
          </Form.Item>
          <Form.Item label='上半天班日期' name='halfDate' trigger='onConfirm'>
            <>
              <Cell
                onClick={() => {
                  if (!form.getFieldValue('fromDate') || !form.getFieldValue('toDate')) {
                    visibleCollection.notifyMassage = '请先选择开始日期和结束日期';
                    visibleCollection.notifyVisible = true;
                  } else {
                    visibleCollection.halfDate = true;
                  }
                }}
                className='leave-container_form_cell'
                title={form.getFieldValue('halfDate') ?? '请选择'}
                extra={
                  <>
                    <View
                      className='leave-container_form_clear'
                      onClick={(e) => {
                        e.stopPropagation();
                        form.setFieldsValue({
                          halfDate: undefined,
                        });
                        setHalfDay('');
                      }}
                    >
                      清除
                    </View>
                    <ArrowRight size={15} />
                  </>
                }
                align='center'
              />
              <Calendar
                startDate={form.getFieldValue('fromDate')}
                endDate={form.getFieldValue('toDate')}
                autoBackfill
                visible={visibleCollection.halfDate}
                onClose={() => (visibleCollection.halfDate = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  form.setFieldsValue({
                    halfDate: current,
                  });
                  setHalfDay(current);
                }}
              />
            </>
          </Form.Item>
          <Form.Item label='总休假天数' name='totalLeaveDays'>
            <Input disabled placeholder='请输入总休假天数' type='text' />
          </Form.Item>
          <Form.Item label='原因' name='description'>
            <TextArea showCount placeholder='请输入原因' />
          </Form.Item>
        </Form>
      </View>
      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleCollection.notifyVisible}
        type='danger'
        onClose={() => {
          visibleCollection.notifyVisible = false;
        }}
      >
        {visibleCollection.notifyMassage}
      </Notify>
    </View>
  );
};

export default Leave;
