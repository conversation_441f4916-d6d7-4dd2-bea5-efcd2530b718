import { useEffect, useMemo, useState } from 'react';
import { Phone } from '@nutui/icons-react-taro';
import { Avatar, Button, Elevator, SearchBar } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useRequest } from 'ahooks';

import { addressBook } from '@/api/auth';
import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight, getGradientColorFromCharCode } from '@/utils';

import './index.scss';

const listHeight = calcAvailableHeight(0, 44);

const AddressBook = () => {
  const navHeight = useSafeStatusbar();

  const [searchValue, setSearchValue] = useState('');
  const [dataList, setDataList] = useState<API.UserAddressBookResp[]>([]);

  const { run: fetchData } = useRequest(
    () =>
      addressBook({
        //@ts-ignore
        name: searchValue,
      }),
    {
      onSuccess: (res) => {
        if (res?.data) {
          setDataList(res.data);
        }
      },
      manual: true,
      debounceWait: 300, // 添加防抖
    },
  );

  useEffect(() => {
    if (typeof window !== 'undefined') {
      fetchData();
    }
  }, [fetchData]);

  const getInitial = (name: string, email?: string): string => {
    // 如果有邮箱，从邮箱中提取姓氏首字母
    if (email) {
      const localPart = email.split('@')[0];
      const nameParts = localPart.split('.');
      if (nameParts.length > 1) {
        const surname = nameParts[1]; // 取.后面的部分作为姓氏
        if (surname) {
          return surname.charAt(0).toUpperCase();
        }
      }
    }

    // 如果没有邮箱或无法从邮箱提取，则使用名字的首字母
    if (!name) return '#';

    const firstChar = name.charAt(0);
    if (/^[a-zA-Z]/.test(firstChar)) {
      return firstChar.toUpperCase();
    }

    // 其他情况（数字、符号等）
    return '#';
  };

  const sortedData = useMemo(() => {
    const sorted = [...dataList].sort((a, b) => {
      const initialA = getInitial(a.username || '', a.email);
      const initialB = getInitial(b.username || '', b.email);
      return (
        initialA.localeCompare(initialB) ||
        (a.username || '').localeCompare(b.username || '', 'zh-Hans-CN')
      );
    });

    const grouped: { title: string; list: API.UserAddressBookResp[] }[] = [];
    sorted.forEach((item) => {
      const initial = getInitial(item.username || '', item.email);
      const existingGroup = grouped.find((group) => group.title === initial);

      if (existingGroup) {
        existingGroup.list.push(item);
      } else {
        grouped.push({
          title: initial,
          list: [item],
        });
      }
    });

    return grouped.sort((a, b) => {
      // 特殊处理 '#' 分组，让它永远在最后
      if (a.title === '#') return 1;
      if (b.title === '#') return -1;
      return a.title.localeCompare(b.title);
    });
  }, [dataList]);

  const handlePhoneCall = (phone?: string) => {
    if (phone) {
      Taro.makePhoneCall({
        phoneNumber: phone,
      });
    }
  };

  return (
    <View className='address-book'>
      <TitleBar title='通讯录' />
      <View
        style={{
          marginTop: navHeight + 10,
        }}
      >
        <SearchBar
          className='search'
          right={
            <View
              onClick={() => {
                fetchData();
              }}
            >
              搜索
            </View>
          }
          value={searchValue}
          placeholder='搜索姓名'
          onChange={async (val) => {
            setSearchValue(val);
          }}
        />
        <View className='content'>
          {sortedData.length > 0 && (
            <Elevator
              height={listHeight - 20 + 'px'}
              list={sortedData}
              onItemClick={(_key: string, _item: any) => {}}
              onIndexClick={(_key: string) => {}}
              showKeys
            >
              <Elevator.Context.Consumer>
                {(value) => {
                  const item = value as API.UserAddressBookResp;
                  return (
                    <View className='contact-item'>
                      <Avatar
                        className='avatar'
                        color='#fff'
                        style={getGradientColorFromCharCode(item?.username?.charCodeAt(1) || 0)}
                      >
                        {item?.username ? item.username.slice(-2) : ''}
                      </Avatar>
                      <View className='info'>
                        <View className='name'>{item?.username}</View>
                        <View className='department'>{item?.department}</View>
                        <View className='email'>{item?.email}</View>
                      </View>
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePhoneCall(item?.phone);
                        }}
                        type='primary'
                        icon={<Phone size='14' />}
                      />
                    </View>
                  );
                }}
              </Elevator.Context.Consumer>
            </Elevator>
          )}
        </View>
      </View>
    </View>
  );
};

export default AddressBook;
