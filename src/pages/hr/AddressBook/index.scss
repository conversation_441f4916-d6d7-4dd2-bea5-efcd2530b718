.address-book {
  height: 100vh;
  padding: 20px;
  background: var(--container-background-color);
  --nutui-elevator-list-item-name-height: 150px;
  --nutui-searchbar-background: #fff;
  --nutui-elevator-list-item-name-height: 110px;

  .search {
    margin-bottom: 10px;
  }
  .contact-item {
    position: relative;
    display: flex;
    align-items: center;
    width: 90%;
    padding: 2px 6px;
    background: #fff;

    &::after {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 6px;
      height: 1px;
      background: #f5f5f5;
      content: '';
    }

    .avatar {
      position: absolute;
      top: 8px;
      left: 5px;
    }

    .info {
      flex: 1;
      min-width: 0;
      margin-left: 50px;
      padding: 4px 0;

      .name {
        margin-bottom: 10px;
        color: #333;
        font-weight: 500;
        font-size: 16px;
      }

      .email {
        margin-bottom: 4px;
        color: #666;
        font-size: 14px;
      }

      .department {
        color: #999;
        font-size: 13px;
        line-height: 1.4;
      }
    }
  }
}
