import { useEffect, useRef } from 'react';
import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import {
  Button,
  Calendar,
  Cell,
  Form,
  Input,
  Notify,
  Radio,
} from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';

import { createLeaveSellMobile } from '@/api/employee';
import { leaveAppPage } from '@/api/leaveApp';
import SearchSelect, { SearchItemProps } from '@/components/SearchSelect';
import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';

import './index.scss';

const LeaveSell = () => {
  const navHeight = useSafeStatusbar();
  const leaveListRef = useRef<(PickerOption & API.LeaveAppPageResp)[]>([]);
  const [form] = Form.useForm();

  // 弹窗开关集合
  const visibleCollection = useReactive({
    sellDate: false,
    leaveAppId: false,
    notifyVisible: false,
    notifyMassage: '请先选择休假申请',
  });

  const { run: runLeave } = useRequest(() => leaveAppPage({ pageNum: 1, pageSize: 10000 }), {
    manual: true,
    onSuccess: (res) => {
      leaveListRef.current = (res?.data?.records as API.LeaveAppPageResp[]).map((i) => ({
        ...i,
        value: i.id as string,
        text: `${i.documentNumber}-${i.leaveTypeName}` as string,
      }));
    },
  });

  const { run: submitForm, loading: submitLoading } = useRequest(
    (value) => createLeaveSellMobile(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '提交成功', icon: 'success' });
          setTimeout(() => {
            Taro.navigateBack();
          }, 1800);
        }
      },
    },
  );

  const submitSucceed = (values: any) => {
    submitForm(values);
  };

  const submitFailed = () => {
    visibleCollection.notifyMassage = '请完善必填项！';
    visibleCollection.notifyVisible = true;
  };

  useEffect(() => {
    runLeave();
  }, [runLeave]);

  return (
    <View className='leave'>
      <TitleBar title='销假' />
      <View className='leave-container' style={{ marginTop: navHeight + 20 }}>
        <Form
          form={form}
          initialValues={{
            half: 0,
          }}
          className='leave-container_form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={submitFailed}
          footer={
            <Button
              loading={submitLoading}
              className='leave-container_form_footer'
              formType='submit'
              type='primary'
            >
              提交
            </Button>
          }
        >
          <Form.Item
            label='休假申请'
            name='leaveAppId'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.leaveAppId = true;
                }}
                className='leave-container_form_cell'
                title={
                  leaveListRef.current.find((i) => i.value === form.getFieldValue('leaveAppId'))
                    ?.documentNumber ?? '请选择'
                }
                extra={<ArrowRight size={15} />}
                align='center'
              />
              {leaveListRef.current?.length && <SearchSelect
                originList={leaveListRef.current as SearchItemProps[]}
                visible={visibleCollection.leaveAppId}
                close={() => {
                  visibleCollection.leaveAppId = false;
                }}
                done={(options: PickerOption[] & API.LeaveAppPageResp[]) => {
                  if (options?.length) {
                    form.setFieldsValue({
                      leaveType: options[0]?.leaveTypeName,
                      leaveAppId: options[0]?.value,
                      fromDate: options[0]?.fromDate,
                      toDate: options[0]?.toDate,
                      totalLeaveDays: options[0]?.totalLeaveDays,
                      leaveAppNumber: options[0]?.documentNumber,
                    });
                  }
                }}
              />
              }
            </>
          </Form.Item>
          <Form.Item label='休假类型' name='leaveType'>
            <Input disabled placeholder='请输入休假类型' type='text' />
          </Form.Item>
          <Form.Item label='休假开始时间' name='fromDate'>
            <Input disabled placeholder='请输入休假开始时间' type='text' />
          </Form.Item>
          <Form.Item label='休假结束时间' name='toDate'>
            <Input disabled placeholder='请输入休假结束时间' type='text' />
          </Form.Item>
          <Form.Item label='总休假天数' name='totalLeaveDays'>
            <Input disabled placeholder='请输入总休假天数' type='text' />
          </Form.Item>
          <Form.Item
            label='销假日期'
            name='sellDate'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  if (!form.getFieldValue('leaveAppId')) {
                    visibleCollection.notifyMassage = '请先选择休假申请';
                    visibleCollection.notifyVisible = true;
                  } else {
                    visibleCollection.sellDate = true;
                  }
                }}
                className='leave-container_form_cell'
                title={form.getFieldValue('sellDate') ?? '请选择'}
                extra={
                  <>
                    <View
                      className='leave-container_form_clear'
                      onClick={(e) => {
                        e.stopPropagation();
                        form.setFieldsValue({
                          sellDate: undefined,
                        });
                      }}
                    >
                      清除
                    </View>
                    <ArrowRight size={15} />
                  </>
                }
                align='center'
              />
              <Calendar
                startDate={form.getFieldValue('fromDate')}
                endDate={form.getFieldValue('toDate')}
                autoBackfill
                visible={visibleCollection.sellDate}
                onClose={() => (visibleCollection.sellDate = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  form.setFieldsValue({
                    sellDate: current,
                  });
                }}
              />
            </>
          </Form.Item>

          <Form.Item label='是否销假半天' name='half'>
            <Radio.Group>
              <Radio icon={<Checklist />} activeIcon={<Checklist />} value={1}>
                是
              </Radio>
              <Radio icon={<Checklist />} activeIcon={<Checklist />} value={0}>
                否
              </Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </View>
      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleCollection.notifyVisible}
        type='danger'
        onClose={() => {
          visibleCollection.notifyVisible = false;
        }}
      >
        {visibleCollection.notifyMassage}
      </Notify>
    </View>
  );
};

export default LeaveSell;
