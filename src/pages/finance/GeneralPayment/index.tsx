import { useEffect } from 'react';
import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import {
  Button,
  Calendar,
  Cell,
  Form,
  Input,
  InputNumber,
  Notify,
  Picker,
  Radio,
} from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { projectPayCreate } from '@/api/fina';
import SearchSelect, { SearchItemProps } from '@/components/SearchSelect';
import TitleBar from '@/components/TitleBar';
import { PAYMENT, PAYMENT_TYPE } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { useAvailableProjectList } from '@/hooks/useAvailableProjectList';
import { useDepartment } from '@/hooks/useDepartment';
import { usePartnerList } from '@/hooks/usePartnerList';
import { useLoginStore } from '@/store';

import './index.scss';

const GeneralPayment = () => {
  const navHeight = useSafeStatusbar();
  const { userInfo } = useLoginStore();
  const { departmentList } = useDepartment();
  const { availableProjectList, loading: availableProjectLoading } = useAvailableProjectList();
  const { partnerList, loading: partnerLoading } = usePartnerList();
  const [form] = Form.useForm();

  const clientList = partnerList.filter((item) => item.partnerType === 'CLIENT'); //客户

  // 弹窗开关集合
  const visibleCollection = useReactive({
    expectedPaymentDate: false,
    paymentDeadline: false,
    projectNumber: false,
    customerId: false,
    notifyVisible: false,
    notifyMassage: '请先选择开始日期和结束日期',
  });

  const { run: submitForm, loading: submitLoading } = useRequest(
    (value) => projectPayCreate(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '提交成功', icon: 'success' });
          setTimeout(() => {
            Taro.navigateBack();
          }, 1800);
        }
      },
    },
  );

  const submitSucceed = (values: any) => {
    submitForm(values);
  };

  const submitFailed = () => {
    visibleCollection.notifyMassage = '请完善必填项！';
    visibleCollection.notifyVisible = true;
  };

  useEffect(() => {
    if (departmentList.length && userInfo) {
      form.setFieldsValue({
        paymentType: 'GENERAL',
        departmentName: userInfo?.department,
        username: userInfo?.username,
        userId: userInfo?.id,
        applicationDate: dayjs().format('YYYY-MM-DD'),
      });
    }
  }, [departmentList, form, userInfo]);

  return (
    <View className='apply'>
      <TitleBar title='项目付款申请' />
      <View className='apply-container' style={{ marginTop: navHeight + 20 }}>
        <Form
          form={form}
          className='apply-container_form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={submitFailed}
          footer={
            <Button
              loading={submitLoading}
              className='apply-container_form_footer'
              formType='submit'
              type='primary'
            >
              提交
            </Button>
          }
        >
          <Form.Item label='申请人' name='username'>
            <Input disabled placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='所属部门' name='departmentName'>
            <Picker options={departmentList}>
              {(value: string) => {
                return (
                  <Cell
                    className='apply-container_form_cell'
                    title={departmentList.find((i) => i.value === value)?.text ?? '请选择'}
                    align='center'
                  />
                );
              }}
            </Picker>
          </Form.Item>
          <Form.Item label='申请日期' name='applicationDate'>
            <Input disabled placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='付款类型'
            name='paymentType'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Radio.Group>
              {PAYMENT_TYPE.map((i) => {
                if (i.value !== 'GOODS') {
                  return (
                    <Radio
                      icon={<Checklist />}
                      activeIcon={<Checklist />}
                      key={i.value}
                      value={i.value}
                    >
                      {i.label}
                    </Radio>
                  );
                }
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='付款方式'
            name='paymentMethod'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Radio.Group>
              {PAYMENT.map((i) => {
                return (
                  <Radio
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='预计付款日期'
            name='expectedPaymentDate'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.expectedPaymentDate = true;
                }}
                className='apply-container_form_cell'
                title={form.getFieldValue('expectedPaymentDate') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Calendar
                autoBackfill
                visible={visibleCollection.expectedPaymentDate}
                onClose={() => (visibleCollection.expectedPaymentDate = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  form.setFieldsValue({
                    expectedPaymentDate: current,
                  });
                }}
              />
            </>
          </Form.Item>
          <Form.Item
            label='付款截止日期'
            name='paymentDeadline'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.paymentDeadline = true;
                }}
                className='apply-container_form_cell'
                title={form.getFieldValue('paymentDeadline') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Calendar
                autoBackfill
                visible={visibleCollection.paymentDeadline}
                onClose={() => (visibleCollection.paymentDeadline = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  form.setFieldsValue({
                    paymentDeadline: current,
                  });
                }}
              />
            </>
          </Form.Item>
          <Form.Item
            label='项目编号'
            name='projectNumber'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  if (availableProjectLoading) {
                    return;
                  }
                  visibleCollection.projectNumber = true;
                }}
                className='apply-container_form_cell'
                title={
                  availableProjectLoading
                    ? '数据准备中'
                    : form.getFieldValue('projectNumber') ?? '请选择'
                }
                extra={<ArrowRight size={15} />}
                align='center'
              />
              {availableProjectList?.length && <SearchSelect
                key='project'
                originList={availableProjectList as SearchItemProps[]}
                visible={visibleCollection.projectNumber}
                close={() => {
                  visibleCollection.projectNumber = false;
                }}
                done={(options: PickerOption[] & API.ProBaseInfoResp[]) => {
                  if (options?.length) {
                    form.setFieldsValue({
                      projectNumber: options[0]?.value,
                      projectName: options[0]?.projectName,
                      projectId: options[0]?.id,
                    });
                  }
                }}
              />
              }
            </>
          </Form.Item>
          <Form.Item label='项目名称' name='projectName'>
            <Input disabled placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='客户名称'
            name='customerId'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  if (partnerLoading) {
                    return;
                  }
                  visibleCollection.customerId = true;
                }}
                className='apply-container_form_cell'
                title={
                  partnerLoading ? '数据准备中' : form.getFieldValue('customerName') ?? '请选择'
                }
                extra={<ArrowRight size={15} />}
                align='center'
              />
              {clientList?.length && <SearchSelect
                key='customer'
                originList={clientList as SearchItemProps[]}
                visible={visibleCollection.customerId}
                close={() => {
                  visibleCollection.customerId = false;
                }}
                done={(options: PickerOption[] & API.PartnerInfoResp[]) => {
                  if (options?.length) {
                    form.setFieldsValue({
                      customerId: options[0]?.value,
                      customerName: options[0]?.clientName,
                    });
                  }
                }}
              />
              }
            </>
          </Form.Item>
          <Form.Item label='说明' name='remarks'>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <View className='apply-container_form_title'>收款账户信息</View>
          <Form.Item
            label='单位名称(账户名)'
            name='accountName'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='账号' name='account' rules={[{ required: true, message: '请选择' }]}>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='开户行'
            name='openingBank'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='付款金额'
            name='paymentAmount'
            rules={[{ required: true, message: '请选择' }]}
          >
            <InputNumber
              min={0}
              max={***************}
              digits={2}
              formatter={(value) => `￥${value}`}
            />
          </Form.Item>
          <Form.Item label='备注' name='directions'>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
        </Form>
      </View>
      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleCollection.notifyVisible}
        type='danger'
        onClose={() => {
          visibleCollection.notifyVisible = false;
        }}
      >
        {visibleCollection.notifyMassage}
      </Notify>
    </View>
  );
};

export default GeneralPayment;
