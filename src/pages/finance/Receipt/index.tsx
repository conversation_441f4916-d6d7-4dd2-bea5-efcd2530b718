import { useEffect } from 'react';
import { Plus } from '@nutui/icons-react-taro';
import { Button, Cell, InfiniteLoading, Skeleton, Tag } from '@nutui/nutui-react-taro';
import { TagType } from '@nutui/nutui-react-taro/dist/types/packages/tag';
import { View } from '@tarojs/components';
import { useReactive, useRequest } from 'ahooks';

import { selectReceiptList } from '@/api/fina';
import TitleBar from '@/components/TitleBar';
import { CLAIM_STATUS } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight } from '@/utils';

import NewReceipt from './components/NewReceipt';

import './index.scss';

interface StateType {
  list: API.ReceiptsResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo: API.ReceiptsResp;
}

const listHeight = calcAvailableHeight(0, 44) + 'px';

const Receipt = () => {
  const navHeight = useSafeStatusbar();

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: {},
    hasMore: true,
  });
  const pagination = useReactive({
    pageNum: 1,
    pageSize: 15,
  });

  const { run: getList, loading } = useRequest(
    () => selectReceiptList({ pageNum: pagination.pageNum, pageSize: pagination.pageSize }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.data?.records && res?.data?.records?.length > 0) {
          if (pagination.pageNum === 1) {
            state.list = res.data?.records;
          } else {
            state.list = [...state.list, ...res?.data?.records];
          }
        } else {
          if (pagination.pageNum === 1) state.list = [];
          state.hasMore = false;
        }
      },
    },
  );

  useEffect(() => {
    getList();
  }, []);

  const loadMore = async () => {
    pagination.pageNum += 1;
    getList();
  };

  const refresh = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  const closeDrawer = () => {
    pagination.pageNum = 1;
    state.currentInfo = {};
    state.drawerVisible = false;
  };

  return (
    <View className='receipt'>
      <TitleBar title='收款记录表' />
      <Button
        className='receipt-add'
        type='primary'
        onClick={() => {
          state.drawerVisible = true;
        }}
      >
        <Plus className='receipt-add_icon' size='50' />
      </Button>
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='receipt-list'
      >
        {loading && pagination.pageNum === 1 ? (
          <Skeleton rows={20} animated />
        ) : (
          <ul id='scrollDemo' className='receipt-ul'>
            <InfiniteLoading
              pullingText='松开刷新'
              loadingText='加载中'
              loadMoreText='没有更多了'
              pullRefresh
              target='scrollDemo'
              hasMore={state.hasMore}
              onLoadMore={loadMore}
              onRefresh={refresh}
            >
              {state.list.map((record: API.ReceiptsResp) => {
                const statusObj = CLAIM_STATUS.find((i) => i.value === record.claimStatus);

                return (
                  <View
                    className='receipt-cell_each'
                    key={record.id}
                    onClick={() => {
                      state.currentInfo = record;
                      state.drawerVisible = true;
                    }}
                  >
                    <Cell.Group divider={false}>
                      <Cell
                        className='receipt-cell_top'
                        title={
                          <View className='receipt-cell_title'>
                            {record.collectNumber || '暂无记录号'}
                          </View>
                        }
                        extra={
                          <Tag
                            className='receipt-cell_tag'
                            type={(statusObj?.status as TagType) || 'primary'}
                          >
                            {statusObj?.label || '未认领'}
                          </Tag>
                        }
                      />
                      <Cell>
                        <View className='receipt-cell-desc'>
                          <View className='receipt-cell_line'>
                            <View className='receipt-cell_line_left'>认领人姓名：</View>
                            <View className='receipt-cell_line_right'>
                              {record.claimUser || '暂无数据'}
                            </View>
                          </View>
                          <View className='receipt-cell_line'>
                            <View className='receipt-cell_line_left'>收款日期：</View>
                            <View className='receipt-cell_line_right'>
                              {record.receiveTime || '暂无数据'}
                            </View>
                          </View>
                          <View className='receipt-cell_line'>
                            <View className='receipt-cell_line_left'>收款金额：</View>
                            <View className='receipt-cell_line_right'>
                              {'¥' + record.receiveAmount || '暂无数据'}
                            </View>
                          </View>
                          <View className='receipt-cell_line'>
                            <View className='receipt-cell_line_left'>单位名称(账户名)：</View>
                            <View className='receipt-cell_line_right'>
                              {record.institutionName || '暂无数据'}
                            </View>
                          </View>
                        </View>
                      </Cell>
                    </Cell.Group>
                  </View>
                );
              })}
            </InfiniteLoading>
          </ul>
        )}
      </View>
      <NewReceipt
        currentInfo={state.currentInfo}
        showBottom={state.drawerVisible}
        closeDrawer={closeDrawer}
        refresh={refresh}
      />
    </View>
  );
};

export default Receipt;
