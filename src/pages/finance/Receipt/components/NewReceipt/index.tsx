import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import {
  Button,
  Cell, DatePicker,
  Form,
  Input,
  InputNumber,
  Notify,
  Popup,
  Radio,
} from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { createCollect } from '@/api/conInvoiced';
import { PAYMENT } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';

import './index.scss';

interface DrawerProps {
  showBottom: boolean;
  closeDrawer: () => void;
  refresh: () => void;
  currentInfo: API.ReceiptsResp;
}

const formatter = (type: string, option: PickerOption) => {
  switch (type) {
    case 'year':
      option.text += '年';
      break;
    case 'month':
      option.text += '月';
      break;
    case 'day':
      option.text += '日';
      break;
    case 'hour':
      option.text += '时';
      break;
    case 'minute':
      option.text += '分';
      break;
    default:
      option.text += '';
  }
  return option;
};

const NewReceipt = (props: DrawerProps) => {
  const { showBottom, closeDrawer, refresh, currentInfo } = props;

  const navHeight = useSafeStatusbar();
  const [form] = Form.useForm();

  // 弹窗开关集合
  const visibleCollection = useReactive({
    receiveTime: false,
    partnerId: false,
    notifyVisible: false,
    notifyMassage: '请先选择日期',
  });

  const { run: submitForm, loading: submitLoading } = useRequest((value) => createCollect(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        Taro.showToast({ title: '提交成功', icon: 'success' });
        closeDrawer();
        refresh();
      }
    },
  });

  const submitSucceed = (values: any) => {
    submitForm(values);
  };

  const submitFailed = () => {
    visibleCollection.notifyMassage = '请完善必填项！';
    visibleCollection.notifyVisible = true;
  };

  return (
    <Popup
      destroyOnClose
      className='pop'
      visible={showBottom}
      position='bottom'
      afterShow={() => {
        if (currentInfo?.id) {
          //编辑
          form.setFieldsValue({ ...currentInfo });
        } else {
          //新建
          form.setFieldsValue({
            inputTime: dayjs().format('YYYY-MM-DD'),
          });
        }
      }}
      onClose={() => {
        closeDrawer();
      }}
    >
      <ScrollView scrollY className='pop-container'>
        <Form
          divider
          form={form}
          className='pop-form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={submitFailed}
          footer={
            currentInfo?.id ? (
              <></>
            ) : (
              <Button
                loading={submitLoading}
                className='pop-form_button'
                formType='submit'
                type='primary'
              >
                提交
              </Button>
            )
          }
        >
          <Form.Item
            label='收款日期'
            name='receiveTime'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            {currentInfo?.id ? (
              <Input placeholder='请输入' disabled type='text' />
            ) : (
              <>
                <Cell
                  onClick={() => {
                    visibleCollection.receiveTime = true;
                  }}
                  className='pop-form_cell'
                  title={form.getFieldValue('receiveTime') ?? '请选择'}
                  extra={<ArrowRight size={15} />}
                  align='center'
                />
                <DatePicker
                  type='datetime'
                  visible={visibleCollection.receiveTime}
                  defaultValue={new Date()}
                  formatter={formatter}
                  onClose={() => (visibleCollection.receiveTime = false)}
                  onConfirm={(_, values) => {
                    const receiveTime = values.slice(0, 3).join('-') + ' ' + values.slice(3).join(':') + ':00';
                    form.setFieldsValue({
                      receiveTime,
                    });
                  }}
                />
              </>
            )}
          </Form.Item>
          <Form.Item
            label='收款方式'
            name='collectionWay'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Radio.Group>
              {PAYMENT.map((i) => {
                return (
                  <Radio
                    disabled={!!currentInfo?.id}
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='收款金额'
            name='receiveAmount'
            rules={[{ required: true, message: '请输入' }]}
          >
            <InputNumber
              disabled={!!currentInfo?.id}
              min={0}
              max={***************}
              digits={2}
              formatter={(value) => `￥${value}`}
            />
          </Form.Item>
          <Form.Item
            label='单位名称(账户名)'
            name='institutionName'
            rules={[{ required: true, message: '请输入' }]}
          >
            <Input placeholder='请输入' disabled={!!currentInfo?.id} type='text' />
          </Form.Item>
          <Form.Item
            label='对方开户银行'
            name='bank'
            rules={[{ required: true, message: '请输入' }]}
          >
            <Input placeholder='请输入' disabled={!!currentInfo?.id} type='text' />
          </Form.Item>
          <Form.Item
            label='对方银行账号'
            name='account'
            rules={[{ required: true, message: '请输入' }]}
          >
            <Input placeholder='请输入' disabled={!!currentInfo?.id} type='text' />
          </Form.Item>
          <Form.Item label='摘要' name='overview'>
            <Input placeholder='请输入' disabled={!!currentInfo?.id} type='text' />
          </Form.Item>
          <Form.Item label='用途' name='useWay'>
            <Input placeholder='请输入' disabled={!!currentInfo?.id} type='text' />
          </Form.Item>
          <Form.Item label='详细信息' name='remark'>
            <Input placeholder='请输入' disabled={!!currentInfo?.id} type='text' />
          </Form.Item>
          <Form.Item label='录入日期' name='inputTime'>
            <Input disabled placeholder='请输入' type='text' />
          </Form.Item>
        </Form>
      </ScrollView>
      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleCollection.notifyVisible}
        type='danger'
        onClose={() => {
          visibleCollection.notifyVisible = false;
        }}
      >
        {visibleCollection.notifyMassage}
      </Notify>
    </Popup>
  );
};

export default NewReceipt;
