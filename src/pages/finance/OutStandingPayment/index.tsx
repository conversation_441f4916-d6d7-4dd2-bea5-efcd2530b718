import { useEffect } from 'react';
import { Cell, InfiniteLoading, Skeleton, Tag } from '@nutui/nutui-react-taro';
import { TagType } from '@nutui/nutui-react-taro/dist/types/packages/tag';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';

import { pagePayment } from '@/api/conInvoiced';
import TitleBar from '@/components/TitleBar';
import { PAYMENT_STATUS } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight } from '@/utils';

import StandingPayment from './components/StandingPayment';

import './index.scss';

interface StateType {
  list: API.PaymentResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo: API.PaymentResp;
}

const listHeight = calcAvailableHeight(0, 44) + 'px';

const Receipt = () => {
  const navHeight = useSafeStatusbar();

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: {},
    hasMore: true,
  });
  const pagination = useReactive({
    pageNum: 1,
    pageSize: 15,
  });

  const { run: getList, loading } = useRequest(
    () => pagePayment({ pageNum: pagination.pageNum, pageSize: pagination.pageSize }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.data?.records && res?.data?.records?.length > 0) {
          //待付款的数据---允许付款而未付款的
          const noPayList = res.data?.records?.filter(
            (item) => item.payStatus !== 'PAY' && item.status === 'ALLOW',
          );
          if (pagination.pageNum === 1) {
            state.list = noPayList;
          } else {
            state.list = [...state.list, ...noPayList];
          }
        } else {
          if (pagination.pageNum === 1) state.list = [];
          state.hasMore = false;
        }
      },
    },
  );

  useEffect(() => {
    getList();
  }, []);

  const loadMore = async () => {
    pagination.pageNum += 1;
    getList();
  };

  const refresh = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  const closeDrawer = () => {
    pagination.pageNum = 1;
    state.currentInfo = {};
    state.drawerVisible = false;
  };

  return (
    <View className='outStanding'>
      <TitleBar
        title='待付款记录表'
        onNavBack={() => {
          Taro.reLaunch({ url: '/pages/index/index' });
        }}
      />
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='outStanding-list'
      >
        {loading && pagination.pageNum === 1 ? (
          <Skeleton rows={20} animated />
        ) : (
          <ul id='scrollDemo' className='outStanding-ul'>
            <InfiniteLoading
              pullingText='松开刷新'
              loadingText='加载中'
              loadMoreText='没有更多了'
              pullRefresh
              target='scrollDemo'
              hasMore={state.hasMore}
              onLoadMore={loadMore}
              onRefresh={refresh}
            >
              {state.list.map((record: API.PaymentResp) => {
                const statusObj = PAYMENT_STATUS.find((i) => i.value === record.payStatus);

                return (
                  <View
                    className='outStanding-cell_each'
                    key={record.id}
                    onClick={() => {
                      state.currentInfo = record;
                      state.drawerVisible = true;
                    }}
                  >
                    <Cell.Group divider={false}>
                      <Cell
                        className='outStanding-cell_top'
                        title={
                          <View className='outStanding-cell_title'>
                            {record.payNumber || '暂无记录号'}
                          </View>
                        }
                        extra={
                          <Tag
                            className='outStanding-cell_tag'
                            type={(statusObj?.status as TagType) || 'primary'}
                          >
                            {statusObj?.label || '未付款'}
                          </Tag>
                        }
                      />
                      <Cell>
                        <View className='outStanding-cell-desc'>
                          <View className='outStanding-cell_line'>
                            <View className='outStanding-cell_line_left'>申请人：</View>
                            <View className='outStanding-cell_line_right'>
                              {record.applicationUser || '暂无数据'}
                            </View>
                          </View>
                          <View className='outStanding-cell_line'>
                            <View className='outStanding-cell_line_left'>计划付款日期：</View>
                            <View className='outStanding-cell_line_right'>
                              {record.estimatePayTime || '暂无数据'}
                            </View>
                          </View>
                          <View className='outStanding-cell_line'>
                            <View className='outStanding-cell_line_left'>付款金额：</View>
                            <View className='outStanding-cell_line_right'>
                              {'¥' + record.payAmount || '暂无数据'}
                            </View>
                          </View>
                          <View className='outStanding-cell_line'>
                            <View className='outStanding-cell_line_left'>单位名称(账户名)：</View>
                            <View className='outStanding-cell_line_right'>
                              {record.institutionName || '暂无数据'}
                            </View>
                          </View>
                        </View>
                      </Cell>
                    </Cell.Group>
                  </View>
                );
              })}
            </InfiniteLoading>
          </ul>
        )}
      </View>
      <StandingPayment
        currentInfo={state.currentInfo}
        showBottom={state.drawerVisible}
        closeDrawer={closeDrawer}
        refresh={refresh}
      />
    </View>
  );
};

export default Receipt;
