import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import {
  Button,
  Calendar,
  Cell,
  Form,
  Input,
  InputNumber,
  Notify,
  Popup,
  Radio,
} from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';
// import { useReactive, useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { updatePayment } from '@/api/conInvoiced';
import { PAYMENT, PAYMENT_STATUS, PAYMENT_TYPE } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';

import './index.scss';

const rules = { required: true, message: '此项为必填项' };

interface DrawerProps {
  showBottom: boolean;
  closeDrawer: () => void;
  refresh: () => void;
  currentInfo: API.PaymentResp;
}

const NewReceipt = (props: DrawerProps) => {
  const { showBottom, closeDrawer, refresh, currentInfo } = props;

  const navHeight = useSafeStatusbar();
  const [form] = Form.useForm();

  // 弹窗开关集合
  const visibleCollection = useReactive({
    payTime: false,
    partnerId: false,
    notifyVisible: false,
    notifyMassage: '请先选择日期',
    estimatePayTime: false,
    currentPayStatus: '',
  });

  const { run: submitForm, loading: submitLoading } = useRequest((value) => updatePayment(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        Taro.showToast({ title: '操作成功', icon: 'success' });
        closeDrawer();
        refresh();
      }
    },
  });

  const submitFailed = () => {
    visibleCollection.notifyMassage = '请完善必填项！';
    visibleCollection.notifyVisible = true;
  };

  return (
    <Popup
      destroyOnClose
      className='pop'
      visible={showBottom}
      position='bottom'
      afterShow={() => {
        form.setFieldsValue({ ...currentInfo });
      }}
      onClose={() => {
        closeDrawer();
      }}
    >
      <ScrollView scrollY className='pop-container'>
        <Form
          divider
          form={form}
          className='pop-form'
          labelPosition='left'
          onFinish={(values) => {
            submitForm(values);
          }}
          onFinishFailed={submitFailed}
          footer={
            <Button
              loading={submitLoading}
              className='pop-form_button'
              formType='submit'
              type='primary'
            >
              提交
            </Button>
          }
          initialValues={{ ...currentInfo, payStatus: 'NOT_PAY' }}
        >
          <Form.Item
            label='计划付款日期'
            name='estimatePayTime'
            trigger='onConfirm'
            rules={[rules]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.estimatePayTime = true;
                }}
                className='pop-form_cell'
                title={form.getFieldValue('estimatePayTime') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Calendar
                autoBackfill
                visible={visibleCollection.estimatePayTime}
                onClose={() => (visibleCollection.estimatePayTime = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  form.setFieldsValue({
                    estimatePayTime: current,
                  });
                }}
              />
            </>
          </Form.Item>
          <Form.Item label='单位名称(账户名)' name='institutionName' trigger='onConfirm'>
            <Input placeholder='请输入' disabled type='text' />
          </Form.Item>
          <Form.Item label='开户银行' name='bank'>
            <Input placeholder='请输入' disabled type='text' />
          </Form.Item>
          <Form.Item label='银行账号' name='account'>
            <Input placeholder='请输入' disabled type='text' />
          </Form.Item>
          <Form.Item label='付款方式' name='payWay'>
            <Radio.Group>
              {PAYMENT.map((i) => {
                return (
                  <Radio
                    key={i.value}
                    disabled={!!currentInfo?.id}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
            <Input placeholder='请输入' disabled type='text' />
          </Form.Item>
          <Form.Item label='付款类型' name='paymentType' rules={[rules]}>
            <Radio.Group>
              {PAYMENT_TYPE.map((i) => {
                return (
                  <Radio
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item label='付款金额' name='payAmount'>
            <InputNumber
              disabled
              min={0}
              max={999999999999999}
              digits={2}
              formatter={(value) => `￥${value}`}
            />
          </Form.Item>
          <Form.Item label='备注' name='remark'>
            <Input placeholder='请输入' disabled type='text' />
          </Form.Item>
          <Form.Item label='用途' name='useWay'>
            <Input placeholder='请输入' disabled type='text' />
          </Form.Item>
          <Form.Item label='申请人' name='applicationUser'>
            <Input placeholder='请输入' disabled type='text' />
          </Form.Item>
          <Form.Item label='付款状态' name='payStatus'>
            <Radio.Group
              onChange={(e) => {
                visibleCollection.currentPayStatus = e as string;
              }}
            >
              {PAYMENT_STATUS.map((i) => {
                return (
                  <Radio
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='付款日期'
            name='payTime'
            trigger='onConfirm'
            rules={visibleCollection.currentPayStatus === 'PAY' ? [rules] : undefined}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.payTime = true;
                }}
                className='pop-form_cell'
                title={form.getFieldValue('payTime') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Calendar
                autoBackfill
                startDate={dayjs().subtract(1, 'year').format('YYYY-MM-DD')}
                visible={visibleCollection.payTime}
                onClose={() => (visibleCollection.payTime = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  form.setFieldsValue({
                    payTime: current,
                  });
                }}
              />
            </>
          </Form.Item>
        </Form>
      </ScrollView>
      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleCollection.notifyVisible}
        type='danger'
        onClose={() => {
          visibleCollection.notifyVisible = false;
        }}
      >
        {visibleCollection.notifyMassage}
      </Notify>
    </Popup>
  );
};

export default NewReceipt;
