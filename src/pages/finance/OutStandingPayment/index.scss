.outStanding {
  height: 100vh;
  padding: 20px;
  background: var(--container-background-color);
  --nutui-skeleton-background: #e8e8e8;

  &-add {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    position: fixed;
    bottom: 30px;
    right: 20px;
    z-index: 9;

    &_icon {
      position: relative;
      top: 5px;
      right: 2px;
    }
  }

  &-ul {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .outStanding-cell_each {
    width: 98%;
    margin-left: 5px;
    background: #fff;
    border-radius: 10px;

    .outStanding-cell_top {
      --nutui-cell-padding: 13px 16px 0 16px;
    }

    .outStanding-cell_title {
      font-weight: bold;
      font-size: 14px;
      white-space: nowrap;
    }

    .outStanding-cell-desc {
      width: 100%;

      .outStanding-cell_line {
        display: flex;
        width: 100%;
        margin: 5px 0;

        &_left {
          width: 35%;
          margin-right: 8px;
          color: #999;
          white-space: nowrap;
        }

        &_right {
          width: 65%;
        }
      }
    }
  }
}
