import { Button, Form, Input, Popup } from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useRequest } from 'ahooks';

import { passPayApplication, selectPaymentById } from '@/api/conInvoiced';

import './index.scss';

interface DrawerProps {
  showBottom: boolean;
  closeDrawer: () => void;
  refresh: () => void;
  currentInfo:
    | (API.PaymentResp & {
        paymentTypeName: string;
        paymentName: string;
        paymentStatusName: string;
      })
    | null;
}

const AllowPayment = (props: DrawerProps) => {
  const { showBottom, closeDrawer, refresh, currentInfo } = props;

  const [form] = Form.useForm();

  const { run: submitForm, loading: submitLoading } = useRequest(
    (value) => passPayApplication(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '操作成功', icon: 'success' });
          closeDrawer();
          refresh();
        }
      },
    },
  );

  const { run: getDetail } = useRequest(
    () => selectPaymentById({ idReq: { id: (currentInfo?.id || '') as string } }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200 && res?.data) {
          form.setFieldsValue({
            ...res.data,
            paymentName: currentInfo?.paymentName,
            paymentTypeName: currentInfo?.paymentTypeName,
            paymentStatusName: currentInfo?.paymentStatusName,
          });
        }
      },
    },
  );

  const submitSucceed = (values: any) => {
    console.warn('=>(index.tsx:70) values', values);
    const res = { ids: [values?.id] };
    submitForm(res);
  };

  return (
    <Popup
      destroyOnClose
      className='pop'
      visible={showBottom}
      position='bottom'
      onOpen={() => {
        getDetail();
      }}
      onClose={() => {
        closeDrawer();
      }}
    >
      <ScrollView scrollY className='pop-container'>
        <Form
          divider
          form={form}
          className='pop-form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          footer={
            <Button
              loading={submitLoading}
              className='pop-form_button'
              formType='submit'
              type='primary'
            >
              允许付款
            </Button>
          }
        >
          <Form.Item label='付款记录号' name='payNumber'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='计划付款日期' name='estimatePayTime'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='单位名称(账户名)' name='institutionName'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='开户银行' name='bank'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='银行账号' name='account'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='付款方式' name='paymentName'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='付款类型' name='paymentTypeName'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='付款金额' name='payAmount'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='备注' name='remark'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='用途' name='useWay'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='申请人' name='applicationUser'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='付款状态' name='paymentStatusName'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='付款日期' name='payTime'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
        </Form>
      </ScrollView>
    </Popup>
  );
};

export default AllowPayment;
