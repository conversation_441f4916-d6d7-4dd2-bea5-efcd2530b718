import { useEffect } from 'react';
import { Cell, InfiniteLoading, Skeleton, Tag } from '@nutui/nutui-react-taro';
import { TagType } from '@nutui/nutui-react-taro/dist/types/packages/tag';
import { View } from '@tarojs/components';
import { useReactive, useRequest } from 'ahooks';

import { pagePaymentMobile } from '@/api/fina';
import TitleBar from '@/components/TitleBar';
import { PAYMENT, PAYMENT_STATUS, PAYMENT_TYPE } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight } from '@/utils';

import AllowPayment from './components/AllowPayment';

import './index.scss';

interface StateType {
  list: API.PaymentResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo:
    | (API.PaymentResp & {
        paymentTypeName: string;
        paymentName: string;
        paymentStatusName: string;
      })
    | null;
}

const listHeight = calcAvailableHeight(0, 44) + 'px';

const PaymentStatement = () => {
  const navHeight = useSafeStatusbar();

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: null,
    hasMore: true,
  });
  const pagination = useReactive({
    pageNum: 1,
    pageSize: 15,
  });

  const { run: getList, loading } = useRequest(
    () =>
      pagePaymentMobile({
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.data?.records && res?.data?.records?.length > 0) {
          if (pagination.pageNum === 1) {
            state.list = res.data?.records;
          } else {
            state.list = [...state.list, ...res?.data?.records];
          }
        } else {
          if (pagination.pageNum === 1) state.list = [];
          state.hasMore = false;
        }
      },
    },
  );

  useEffect(() => {
    getList();
  }, []);

  const loadMore = async () => {
    pagination.pageNum += 1;
    getList();
  };

  const refresh = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  const closeDrawer = () => {
    pagination.pageNum = 1;
    state.currentInfo = null;
    state.drawerVisible = false;
  };

  return (
    <View className='statement'>
      <TitleBar title='付款记录表' />
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='statement-list'
      >
        {loading && pagination.pageNum === 1 ? (
          <Skeleton rows={20} animated />
        ) : (
          <ul id='scrollDemo' className='statement-ul'>
            <InfiniteLoading
              pullingText='松开刷新'
              loadingText='加载中'
              loadMoreText='没有更多了'
              pullRefresh
              target='scrollDemo'
              hasMore={state.hasMore}
              onLoadMore={loadMore}
              onRefresh={refresh}
            >
              {state.list.map((record: API.PaymentResp) => {
                const statusObj = PAYMENT_STATUS.find((i) => i.value === record.payStatus);
                const paymentType = PAYMENT_TYPE.find((i) => i.value === record.paymentType);
                const payment = PAYMENT.find((i) => i.value === record.payWay);

                return (
                  <View
                    className='statement-cell_each'
                    key={record.id}
                    onClick={() => {
                      state.currentInfo = {
                        ...record,
                        paymentTypeName: paymentType?.label || '',
                        paymentName: payment?.label || '',
                        paymentStatusName: statusObj?.label || '',
                      };
                      state.drawerVisible = true;
                    }}
                  >
                    <Cell.Group divider={false}>
                      <Cell
                        className='statement-cell_top'
                        title={
                          <View className='statement-cell_title'>
                            {record.payNumber || '暂无付款编号'}
                          </View>
                        }
                        extra={
                          <Tag
                            className='statement-cell_tag'
                            type={(statusObj?.status as TagType) || 'primary'}
                          >
                            {statusObj?.label || '未付款'}
                          </Tag>
                        }
                      />
                      <Cell>
                        <View className='statement-cell-desc'>
                          <View className='statement-cell_line'>
                            <View className='statement-cell_line_left'>付款类型：</View>
                            <View className='statement-cell_line_right'>
                              {paymentType?.label || '暂无数据'}
                            </View>
                          </View>
                          <View className='statement-cell_line'>
                            <View className='statement-cell_line_left'>是否允许付款：</View>
                            <View className='statement-cell_line_right'>
                              {record.status === 'ALLOW' ? '是' : '否'}
                            </View>
                          </View>
                          <View className='statement-cell_line'>
                            <View className='statement-cell_line_left'>付款方式：</View>
                            <View className='statement-cell_line_right'>
                              {payment?.label || '暂无数据'}
                            </View>
                          </View>
                          <View className='statement-cell_line'>
                            <View className='statement-cell_line_left'>计划付款日期：</View>
                            <View className='statement-cell_line_right'>
                              {record.estimatePayTime || '暂无数据'}
                            </View>
                          </View>
                          <View className='statement-cell_line'>
                            <View className='statement-cell_line_left'>付款金额：</View>
                            <View className='statement-cell_line_right'>
                              {'¥' + record.payAmount || '暂无数据'}
                            </View>
                          </View>
                          <View className='statement-cell_line'>
                            <View className='statement-cell_line_left'>申请人：</View>
                            <View className='statement-cell_line_right'>
                              {record.applicationUser || '暂无数据'}
                            </View>
                          </View>
                          <View className='statement-cell_line'>
                            <View className='statement-cell_line_left'>申请编号：</View>
                            <View className='statement-cell_line_right'>
                              {record.paymentAppNumber || '暂无数据'}
                            </View>
                          </View>
                        </View>
                      </Cell>
                    </Cell.Group>
                  </View>
                );
              })}
            </InfiniteLoading>
          </ul>
        )}
      </View>
      <AllowPayment
        currentInfo={state.currentInfo}
        showBottom={state.drawerVisible}
        closeDrawer={closeDrawer}
        refresh={refresh}
      />
    </View>
  );
};

export default PaymentStatement;
