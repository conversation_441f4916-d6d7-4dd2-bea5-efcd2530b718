import { useState } from 'react';
import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import { Button, Cell, Form, Notify, Popup, Radio } from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';

import { confirmCollect } from '@/api/conInvoiced';
import { selectReceipt } from '@/api/fina';
import MultiSelect from '@/components/MultiSearchSelect';
import SearchSelect, { SearchItemProps } from '@/components/SearchSelect';
import { COLLECTION_TYPE } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { useLoginStore } from '@/store';

import './index.scss';

interface DrawerProps {
  showBottom: boolean;
  closeDrawer: () => void;
  refresh: () => void;
  currentInfo: API.ReceiptsResp;
}

interface ListProps {
  contractList: API.CollectionPlanResp[] & PickerOption[];
  earnestList: API.PaymentApplicationPageResp[] & PickerOption[];
  contractTitle: string;
}

const ClaimReceipt = (props: DrawerProps) => {
  const { showBottom, closeDrawer, refresh, currentInfo } = props;
  const { userInfo } = useLoginStore();

  const navHeight = useSafeStatusbar();
  const [form] = Form.useForm();
  const [type, setType] = useState<'CONTRACT' | 'MARGIN'>('CONTRACT');

  const state = useReactive<ListProps>({
    contractList: [],
    earnestList: [],
    contractTitle: '请选择',
  });

  // 弹窗开关集合
  const visibleCollection = useReactive({
    collPlanId: false,
    marginId: false,
    notifyVisible: false,
    notifyMassage: '请先选择日期',
  });

  const { run: submitForm, loading: submitLoading } = useRequest((value) => confirmCollect(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        Taro.showToast({ title: '认领成功', icon: 'success' });
        closeDrawer();
        refresh();
      }
    },
  });

  const { run: getDetail, loading: detailLoading } = useRequest(
    () => selectReceipt({ idReq: { id: currentInfo.id as string } }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          state.contractList =
            res?.data?.collectionPlanRespList?.map((i) => ({
              ...i,
              text: `${i.contractNumber}第${i.period}期,预计收款金额￥${i.estimateReAmount}` || '',
              value: i.id || '',
            })) || [];
          state.earnestList =
            res?.data?.paymentApplicationPageRespList?.map((i) => ({
              ...i,
              text: i.documentNumber || '',
              value: i.documentNumber || '',
            })) || [];
          setType(res?.data?.collectionType === 'MARGIN' ? 'MARGIN' : 'CONTRACT');
          form.setFieldsValue({
            ...currentInfo,
            claimTime: Date.now(),
            collectionType: currentInfo?.collectionType || 'CONTRACT',
            collectionWay: currentInfo?.collectionWay || 'FP',
          });
          if (currentInfo?.collectionType === 'CONTRACT') {
            state.contractTitle = currentInfo?.contractNumber || '请选择';
          } else if (currentInfo?.collectionType === 'MARGIN') {
            state.contractTitle = currentInfo?.documentNumber || '请选择';
          }
        }
      },
    },
  );

  const submitSucceed = (values: any) => {
    const { claimTime, collectionType } = values;

    let res: API.ReceiptsReq = {
      ...currentInfo,
      claimUserId: userInfo?.id,
      claimUser: userInfo?.username,
      claimStatus: 'CLAIM',
      claimTime,
      collectionType,
    };
    if (collectionType === 'CONTRACT') {
      const contractArr = values?.collPlan?.map((item: API.CollectionPlanResp) => {
        const { contractName, contractNumber, contractId, id: collPlanId } = item;
        return {
          contractName,
          contractNumber,
          contractId,
          collPlanId,
        };
      });
      res = {
        ...res,
        collPlan: contractArr,
      };
    } else {
      res = {
        ...res,
        marginId: values?.marginId,
        documentNumber: values?.documentNumber,
      };
    }
    submitForm(res);
  };

  const submitFailed = () => {
    visibleCollection.notifyMassage = '请完善必填项！';
    visibleCollection.notifyVisible = true;
  };

  return (
    <Popup
      destroyOnClose
      className='pop'
      visible={showBottom}
      position='bottom'
      onOpen={() => {
        getDetail();
      }}
      onClose={() => {
        closeDrawer();
      }}
    >
      <ScrollView scrollY className='pop-container'>
        <Form
          divider
          form={form}
          className='pop-form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={submitFailed}
          footer={
            currentInfo?.claimStatus === 'CLAIM' ? (
              <></>
            ) : (
              <Button
                loading={submitLoading}
                className='pop-form_button'
                formType='submit'
                type='primary'
              >
                提交
              </Button>
            )
          }
        >
          <Form.Item
            label='收款类型'
            name='collectionType'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Radio.Group
              onChange={(value) => {
                if (value) {
                  setType(value as 'CONTRACT' | 'MARGIN');
                  state.contractTitle = '请选择';
                }
              }}
            >
              {COLLECTION_TYPE.map((i) => {
                return (
                  <Radio
                    disabled={currentInfo?.claimStatus === 'CLAIM'}
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          {type === 'CONTRACT' ? (
            <Form.Item
              label='收款期次'
              name='collPlan'
              trigger='onConfirm'
              rules={[{ required: true, message: '请选择' }]}
            >
              <>
                <Cell
                  onClick={() => {
                    if (detailLoading || currentInfo?.claimStatus === 'CLAIM') {
                      return;
                    }
                    visibleCollection.collPlanId = true;
                  }}
                  className='pop-form_cell'
                  title={detailLoading ? '数据准备中' : state.contractTitle ?? '请选择'}
                  extra={<ArrowRight size={15} />}
                  align='center'
                />
                {state.contractList?.length ? (
                  <MultiSelect
                    key='contract'
                    originList={state.contractList as SearchItemProps[]}
                    visible={visibleCollection.collPlanId}
                    close={() => {
                      visibleCollection.collPlanId = false;
                    }}
                    done={(options: PickerOption[] & API.CollectionPlanResp[]) => {
                      if (options?.length) {
                        form.setFieldsValue({
                          collPlan: options,
                        });
                        state.contractTitle = options
                          .map((i) => {
                            return i.text;
                          })
                          ?.join('、') as string;
                      }
                    }}
                  />
                ) : null}
              </>
            </Form.Item>
          ) : (
            <Form.Item
              label='保证金'
              name='marginId'
              trigger='onConfirm'
              rules={[{ required: true, message: '请选择' }]}
            >
              <>
                <Cell
                  onClick={() => {
                    if (detailLoading || currentInfo?.claimStatus === 'CLAIM') {
                      return;
                    }
                    visibleCollection.marginId = true;
                  }}
                  className='pop-form_cell'
                  title={detailLoading ? '数据准备中' : state.contractTitle ?? '请选择'}
                  extra={<ArrowRight size={15} />}
                  align='center'
                />
                {state.earnestList?.length ? (
                  <SearchSelect
                    key='margin'
                    originList={state.earnestList as SearchItemProps[]}
                    visible={visibleCollection.marginId}
                    close={() => {
                      visibleCollection.marginId = false;
                    }}
                    done={(options: PickerOption[] & API.PaymentApplicationPageResp[]) => {
                      if (options?.length) {
                        form.setFieldsValue({
                          marginId: options[0].id,
                          documentNumber: options[0].documentNumber,
                        });
                        state.contractTitle = options[0].documentNumber as string;
                      }
                    }}
                  />
                ) : null}
              </>
            </Form.Item>
          )}
        </Form>
      </ScrollView>
      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleCollection.notifyVisible}
        type='danger'
        onClose={() => {
          visibleCollection.notifyVisible = false;
        }}
      >
        {visibleCollection.notifyMassage}
      </Notify>
    </Popup>
  );
};

export default ClaimReceipt;
