import { useEffect } from 'react';
import { Cell, InfiniteLoading, Skeleton, Tag } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useReactive, useRequest } from 'ahooks';

import { contractPayList } from '@/api/fina';
import TitleBar from '@/components/TitleBar';
import { useSafeStatusbar } from '@/hooks';
import { calcAvailableHeight } from '@/utils';

import PaymentDrawer from './components/PaymentDrawer';

import './index.scss';

interface StateType {
  list: API.ContractPayResp[] | [];
  drawerVisible: boolean;
  hasMore: boolean;
  currentInfo: API.ContractPayResp;
}
const listHeight = calcAvailableHeight(0, 44) + 'px';

const ApplyPayment = () => {
  const navHeight = useSafeStatusbar();

  const state = useReactive<StateType>({
    list: [],
    drawerVisible: false,
    currentInfo: {},
    hasMore: true,
  });
  const pagination = useReactive({
    pageNum: 1,
    pageSize: 15,
  });

  const { run: getList, loading } = useRequest(
    () => contractPayList({ pageNum: pagination.pageNum, pageSize: pagination.pageSize }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.data?.records && res?.data?.records?.length > 0) {
          if (pagination.pageNum === 1) {
            state.list = res.data?.records;
          } else {
            state.list = [...state.list, ...res?.data?.records];
          }
        } else {
          if (pagination.pageNum === 1) state.list = [];
          state.hasMore = false;
        }
      },
    },
  );

  useEffect(() => {
    getList();
  }, []);

  const loadMore = async () => {
    pagination.pageNum += 1;
    getList();
  };

  const refresh = async () => {
    pagination.pageNum = 1;
    state.hasMore = true;
    getList();
  };

  const closeDrawer = () => {
    pagination.pageNum = 1;
    state.currentInfo = {};
    state.drawerVisible = false;
  };

  return (
    <View className='apply'>
      <TitleBar title='付款计划详细信息表' />
      <View
        style={{
          marginTop: navHeight,
          height: listHeight,
        }}
        className='apply-list'
      >
        {loading && pagination.pageNum === 1 ? (
          <Skeleton rows={20} animated />
        ) : (
          <ul id='scrollDemo' className='apply-ul'>
            <InfiniteLoading
              pullingText='松开刷新'
              loadingText='加载中'
              loadMoreText='没有更多了'
              pullRefresh
              target='scrollDemo'
              hasMore={state.hasMore}
              onLoadMore={loadMore}
              onRefresh={refresh}
            >
              {state.list.map((record: API.ContractPayResp) => {
                return (
                  <View
                    className='apply-cell_each'
                    key={record.id}
                    onClick={() => {
                      state.currentInfo = record;
                      state.drawerVisible = true;
                    }}
                  >
                    <Cell.Group divider={false}>
                      <Cell
                        className='apply-cell_top'
                        title={
                          <View className='apply-cell_title'>
                            <View>{record.contractNumber || '暂无编号'}</View>
                          </View>
                        }
                        extra={
                          <Tag className='apply-cell_tag' type='primary'>
                            {record.estimatePayTime || '暂无日期'}
                          </Tag>
                        }
                      />
                      <Cell>
                        <View className='apply-cell-desc'>
                          <View className='apply-cell_line'>
                            <View className='apply-cell_line_left'>合同名称：</View>
                            <View className='apply-cell_line_right'>
                              {record.contractName || '暂无数据'}
                            </View>
                          </View>
                          <View className='apply-cell_line'>
                            <View className='apply-cell_line_left'>计划付款金额：</View>
                            <View className='apply-cell_line_right'>
                              {record.estimatePayAmount || '暂无数据'}
                            </View>
                          </View>
                        </View>
                      </Cell>
                    </Cell.Group>
                  </View>
                );
              })}
            </InfiniteLoading>
          </ul>
        )}
      </View>
      <PaymentDrawer
        showRight={state.drawerVisible}
        currentInfo={state.currentInfo}
        closeDrawer={closeDrawer}
        refresh={refresh}
      />
    </View>
  );
};

export default ApplyPayment;
