import { useEffect } from 'react';
import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import {
  Button,
  Calendar,
  Cell,
  Form,
  Input,
  InputNumber,
  Notify,
  Popup,
  Radio,
} from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';

import { selectPaymentAppById } from '@/api/conInvoiced';
import { contractPayCreate } from '@/api/fina';
import { PAYMENT } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { useLoginStore } from '@/store';

import './index.scss';

interface InvoiceDrawerProps {
  showRight: boolean;
  closeDrawer: () => void;
  refresh: () => void;
  currentInfo: API.PendingInvoiceResp;
}

interface InvoiceDrawerState {
  details: API.InvoicingResp | {};
  banks: API.BanksDto[] | [];
  notifyMassage: string;
  notifyVisible: boolean;
  collectionBank: boolean;
  countInvoicedTime: boolean;
}

const PaymentDrawer = (props: InvoiceDrawerProps) => {
  const { currentInfo, showRight, closeDrawer, refresh } = props;
  const navHeight = useSafeStatusbar();
  const store = useLoginStore();

  const [form] = Form.useForm();
  const state = useReactive<InvoiceDrawerState>({
    details: {},
    banks: [],
    notifyMassage: '',
    notifyVisible: false,
    collectionBank: false,
    countInvoicedTime: false,
  });

  // 获取详情
  const { run: getDetail } = useRequest((values) => selectPaymentAppById(values), {
    manual: true,
    onSuccess: (res) => {
      state.details = res.data || {};
      form.setFieldsValue({
        ...res.data,
        payStatus: res?.data?.payStatus || 'NOT_PAY',
        operateStatus: 'SUBMIT',
        payWay: res?.data?.payWay || 'FP',
        applicationUser: res?.data?.applicationUser || store.userInfo?.username,
        applicationUserId: res?.data?.applicationUserId || store.userInfo?.id,
      });
    },
  });

  const { run: submitForm, loading: submitLoading } = useRequest(
    (value) => contractPayCreate(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '提交成功', icon: 'success' });
          closeDrawer();
          refresh();
        }
      },
    },
  );

  useEffect(() => {
    if (showRight && currentInfo) {
      getDetail({ id: currentInfo.id });
    }
  }, [showRight, currentInfo]);

  const submitSucceed = (values: any) => {
    console.warn('🚀 ~ submitSucceed ~ values:', values);
    submitForm(values);
  };
  const submitFailed = () => {
    state.notifyMassage = '请完善必填项！';
    state.notifyVisible = true;
  };

  return (
    <Popup
      className='pop'
      visible={showRight}
      position='bottom'
      onClose={() => {
        closeDrawer();
      }}
    >
      <ScrollView scrollY className='pop-container'>
        <Form
          divider
          form={form}
          className='pop-form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={submitFailed}
          footer={
            <Button
              loading={submitLoading}
              className='pop-form_button'
              formType='submit'
              type='primary'
            >
              提交
            </Button>
          }
        >
          <Form.Item label='合同编号' name='contractNumber'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='合同名称' name='contractName'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='计划付款日期' name='estimatePayTime'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='申请人' name='applicationUser'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item
            label='单位名称(账户名)'
            name='institutionName'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='开户银行' name='bank' rules={[{ required: true, message: '请选择' }]}>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='银行账号'
            name='account'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='付款方式' name='payWay'>
            <Radio.Group>
              {PAYMENT.map((i) => {
                return (
                  <Radio
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='付款金额'
            name='payAmount'
            rules={[{ required: true, message: '请选择' }]}
          >
            <InputNumber
              min={0}
              max={***************}
              digits={2}
              formatter={(value) => `￥${value}`}
            />
          </Form.Item>
          <Form.Item label='对方开票日期' name='countInvoicedTime' trigger='onConfirm'>
            <>
              <Cell
                onClick={() => {
                  state.countInvoicedTime = true;
                }}
                className='pop-form_cell'
                title={form.getFieldValue('countInvoicedTime') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Calendar
                autoBackfill
                visible={state.countInvoicedTime}
                onClose={() => (state.countInvoicedTime = false)}
                onConfirm={(p) => {
                  const current = p?.[3]?.replace(/\/+/g, '-');
                  form.setFieldsValue({
                    countInvoicedTime: current,
                  });
                }}
              />
            </>
          </Form.Item>
          <Form.Item label='收票金额' name='receiptAmount'>
            <InputNumber
              min={0}
              max={***************}
              digits={2}
              formatter={(value) => `￥${value}`}
            />
          </Form.Item>
          <Form.Item label='备注' name='remark'>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='用途' name='useWay'>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
        </Form>
      </ScrollView>
      <Notify
        style={{ marginTop: navHeight }}
        visible={state.notifyVisible}
        type='danger'
        onClose={() => {
          state.notifyVisible = false;
        }}
      >
        {state.notifyMassage}
      </Notify>
    </Popup>
  );
};

export default PaymentDrawer;
