import { useEffect } from 'react';
import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import {
  Button,
  Cell,
  Form,
  Input,
  InputNumber,
  Notify,
  Popup,
  Radio,
} from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { ScrollView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { selectInvoicedById } from '@/api/conInvoiced';
import { applicationTicket } from '@/api/contract';
import { getInvoicingBankOption } from '@/api/dictionary';
import SearchSelect, { SearchItemProps } from '@/components/SearchSelect';
import { INVOICE_TYPE } from '@/enums/web';
import { useSafeStatusbar } from '@/hooks';
import { calcTaxRate } from '@/utils';

import './index.scss';

interface BankOption extends API.BanksDto {
  text: string;
  value: string;
}

interface InvoiceDrawerProps {
  showRight: boolean;
  closeDrawer: () => void;
  refresh: () => void;
  currentInfo: API.PendingInvoiceResp;
}

interface InvoiceDrawerState {
  details: API.InvoicingResp | {};
  banks: BankOption[] | [];
  notifyMassage: string;
  notifyVisible: boolean;
  collectionBank: boolean;
}

const InvoiceDrawer = (props: InvoiceDrawerProps) => {
  const { currentInfo, showRight, closeDrawer, refresh } = props;
  const navHeight = useSafeStatusbar();

  const [form] = Form.useForm();
  const state = useReactive<InvoiceDrawerState>({
    details: {},
    banks: [],
    notifyMassage: '',
    notifyVisible: false,
    collectionBank: false,
  });

  // 获取详情
  const { run: getDetail } = useRequest((values) => selectInvoicedById(values), {
    manual: true,
    onSuccess: (res) => {
      state.details = res.data || {};
      form.setFieldsValue(
        {
          ...res.data,
          ticketType: INVOICE_TYPE[0].value,
          applicationTime: res?.data?.applicationTime || new Date(),
          status: res?.data?.status || 'NOT_TICKET',
          operateStatus: 'SUBMIT',
        } || {},
      );
    },
  });

  const { run: submitForm, loading: submitLoading } = useRequest(
    (value) => applicationTicket(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '提交成功', icon: 'success' });
          closeDrawer();
          refresh();
        }
      },
    },
  );

  //开票银行
  const { run: getBanks, loading: banksLoading } = useRequest(getInvoicingBankOption, {
    manual: true,
    onSuccess: (res) => {
      state.banks =
        res?.data?.map((i) => ({
          ...i,
          value: i.bankAs,
          text: i.bankAs,
        })) as BankOption[] || [];
    },
  });

  useEffect(() => {
    if (showRight && currentInfo) {
      getDetail({ id: currentInfo.id });
      getBanks();
    }
  }, [showRight, currentInfo]);

  const submitSucceed = (values: any) => {
    submitForm(values);
  };
  const submitFailed = () => {
    state.notifyMassage = '请完善必填项！';
    state.notifyVisible = true;
  };

  return (
    <Popup
      className='pop'
      visible={showRight}
      position='bottom'
      onClose={() => {
        closeDrawer();
      }}
    >
      <ScrollView scrollY className='pop-container'>
        <Form
          divider
          form={form}
          className='pop-form'
          labelPosition='left'
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={submitFailed}
          footer={
            <Button
              loading={submitLoading}
              className='pop-form_button'
              formType='submit'
              type='primary'
            >
              提交
            </Button>
          }
        >
          <Form.Item label='关联合同编号' name='contractNumber'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='销售' name='salePerson'>
            <Input disabled placeholder='' type='text' />
          </Form.Item>
          <Form.Item label='开票申请日期' name='applicationTime'>
            <Cell
              className='pop-form_cell'
              title={dayjs(form.getFieldValue('applicationTime')).format('YYYY-MM-DD') ?? '请选择'}
              align='center'
            />
          </Form.Item>
          <Form.Item
            label='开票类型'
            name='ticketType'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Radio.Group>
              {INVOICE_TYPE.map((i) => {
                return (
                  <Radio
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='单位名称'
            name='institutionName'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='纳税人识别号'
            name='taxpayerNum'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='开户银行' name='bank' rules={[{ required: true, message: '请选择' }]}>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='银行账号'
            name='account'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='联系电话'
            name='contactNumber'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='注册地址'
            name='registeredAddress'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='开票内容'
            name='ticketContent'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='规格型号' name='model' rules={[{ required: true, message: '请选择' }]}>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='单位' name='unit' rules={[{ required: true, message: '请选择' }]}>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='数量' name='quantity' rules={[{ required: true, message: '请选择' }]}>
            <Input placeholder='请输入' type='digit' />
          </Form.Item>
          <Form.Item
            label='金额'
            name='ticketAmount'
            rules={[{ required: true, message: '请选择' }]}
          >
            <InputNumber
              min={0}
              max={999999999999999}
              digits={2}
              onChange={(value) => {
                const { taxInclusiveAmount } = calcTaxRate(
                  Number(value) || 0,
                  form.getFieldValue('rate') || 0,
                );
                form.setFieldsValue({ rateAmount: taxInclusiveAmount });
              }}
              formatter={(value) => `￥${value}`}
            />
          </Form.Item>
          <Form.Item label='税率' name='rate' rules={[{ required: true, message: '请选择' }]}>
            <Input
              placeholder='请输入'
              type='digit'
              onChange={(value) => {
                const { taxInclusiveAmount } = calcTaxRate(
                  form.getFieldValue('ticketAmount') || 0,
                  Number(value) || 0,
                );
                form.setFieldsValue({ rateAmount: taxInclusiveAmount });
              }}
            />
          </Form.Item>
          <Form.Item label='税额' name='rateAmount' rules={[{ required: true, message: '请选择' }]}>
            <InputNumber min={0} digits={2} disabled />
          </Form.Item>
          <Form.Item label='备注' name='remark'>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <View className='pop-form_title'>销货单位开票信息</View>
          <Form.Item
            label='开票银行别名'
            name='collectionBank'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  if (banksLoading) {
                    return;
                  }
                  state.collectionBank = true;
                }}
                className='pop-form_cell'
                title={banksLoading ? '数据准备中' : form.getFieldValue('collectionBank') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              {state.banks?.length && <SearchSelect
                originList={state.banks as SearchItemProps[]}
                visible={state.collectionBank}
                close={() => {
                  state.collectionBank = false;
                }}
                done={(options: (PickerOption & BankOption)[]) => {
                  if (options?.length) {
                    const {
                      text = '',
                      institutionName = '',
                      taxpayerNum = '',
                      bank = '',
                      account = '',
                      contactNumber = '',
                      registeredAddress = '',
                    } = options[0] || {};
                    form.setFieldsValue({
                      collectionBank: text,
                      salesFirm: institutionName,
                      salesTaxpayerNum: taxpayerNum,
                      salesAccount: `${bank}${account}`,
                      salesContact: `${registeredAddress}${contactNumber}`,
                    });
                  }
                }}
              />
              }
            </>
          </Form.Item>
          <Form.Item label='名称' name='salesFirm' rules={[{ required: true, message: '请选择' }]}>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='纳税人识别号'
            name='salesTaxpayerNum'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='地址、电话'
            name='salesContact'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='开户行及账号'
            name='salesAccount'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Input placeholder='请输入' type='text' />
          </Form.Item>
        </Form>
      </ScrollView>
      <Notify
        style={{ marginTop: navHeight }}
        visible={state.notifyVisible}
        type='danger'
        onClose={() => {
          state.notifyVisible = false;
        }}
      >
        {state.notifyMassage}
      </Notify>
    </Popup>
  );
};

export default InvoiceDrawer;
