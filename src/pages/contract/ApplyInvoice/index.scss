.apply {
  height: 100vh;
  padding: 20px;
  background: var(--container-background-color);
  --nutui-skeleton-background: #e8e8e8;
  &-ul {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .apply-cell_each {
    width: 98%;
    margin-left: 5px;
    background: #fff;
    border-radius: 10px;

    .apply-cell_top {
      --nutui-cell-padding: 13px 16px 0 16px;
    }

    .apply-cell_title {
      font-weight: bold;
      font-size: 14px;
    }

    .apply-cell-desc {
      width: 100%;
      .apply-cell_line {
        display: flex;
        width: 100%;
        margin: 5px 0;
        &_left {
          width: 30%;
          margin-right: 8px;
          color: #999;
          white-space: nowrap;
        }
        &_right {
          width: 70%;
        }
      }
    }
  }
}
