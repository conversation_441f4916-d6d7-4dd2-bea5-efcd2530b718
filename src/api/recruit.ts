// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 创建招聘申请 POST /api/employee/recruit/recruit-create */
export async function createRecruit(body: API.RecruitInsertReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/recruit/recruit-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 招聘申请删除 POST /api/employee/recruit/recruit-del */
export async function recruitDel(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/recruit/recruit-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 招聘申请详情 POST /api/employee/recruit/recruit-info */
export async function recruitInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultRecruitInfoResp>('/api/employee/recruit/recruit-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 招聘申请分页 POST /api/employee/recruit/recruit-page */
export async function recruitPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageRecruitInfoResp>('/api/employee/recruit/recruit-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新招聘申请 POST /api/employee/recruit/recruit-update */
export async function updateRecruit(body: API.RecruitUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/recruit/recruit-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
