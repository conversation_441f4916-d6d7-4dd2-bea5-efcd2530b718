// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 我的任务-统计 GET /api/task/dashboard/task-count */
export async function taskCount(options?: { [key: string]: any }) {
  return request<API.ResultTaskCountResp>('/api/task/dashboard/task-count', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 我的任务-分页 POST /api/task/dashboard/task-dashboard */
export async function taskDashboard(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageTaskPageResp>('/api/task/dashboard/task-dashboard', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 任务日志 POST /api/task/log/list */
export async function taskLogList(body: API.TaskLogReq, options?: { [key: string]: any }) {
  return request<API.ResultListTaskLogListResp>('/api/task/log/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 接受任务 POST /api/task/task/accept */
export async function accept(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/accept', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 指派任务 POST /api/task/task/assign */
export async function assign(body: API.TaskAssignReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/assign', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 退回任务 POST /api/task/task/back */
export async function backTask(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/back', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 关闭任务 POST /api/task/task/closed */
export async function closed(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/closed', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 完成任务 POST /api/task/task/finish */
export async function finish(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/finish', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发布任务 POST /api/task/task/publish */
export async function publishTask(body: API.TaskPublishReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/publish', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分解任务 POST /api/task/task/splitTask */
export async function splitTask(body: API.TaskSplitReq[], options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/splitTask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除任务 POST /api/task/task/task-delete */
export async function taskDelete(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/task-delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 任务详情 POST /api/task/task/task-info */
export async function taskInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultTaskPageResp>('/api/task/task/task-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 任务分页 POST /api/task/task/task-page */
export async function taskPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageTaskPageResp>('/api/task/task/task-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 终止任务 POST /api/task/task/terminated */
export async function terminated(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/terminated', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新任务 POST /api/task/task/update */
export async function updateTask(body: API.TaskUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/task/task/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新子任务 POST /api/task/task/update-children */
export async function updateChildren(
  body: API.TaskUpdateChildrenReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/task/task/update-children', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
