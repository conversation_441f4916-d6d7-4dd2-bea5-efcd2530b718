// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 保证金、押金转换 POST /api/fina/payment-application/converted */
export async function payTypeConverted(
  body: API.PayTypeConvertedReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/fina/payment-application/converted', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 销账 POST /api/fina/payment-application/logoff */
export async function logoff(body: API.LogoffReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/payment-application/logoff', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 销账列表(预退款流水表 POST /api/fina/payment-application/logoff-list */
export async function logoffList(body: API.LogoffListReq, options?: { [key: string]: any }) {
  return request<API.ResultPaymentApplicationLogoffResp>(
    '/api/fina/payment-application/logoff-list',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 新增支付申请 POST /api/fina/payment-application/payment-application-create */
export async function paymentApplicationCreate(
  body: API.PaymentApplicationInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/fina/payment-application/payment-application-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除支付申请   POST /api/fina/payment-application/payment-application-del */
export async function paymentApplicationDel(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/fina/payment-application/payment-application-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 认领下拉列表 GET /api/fina/payment-application/payment-application-drop-down-list */
export async function paymentApplicationDropDownList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentApplicationDropDownListParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListPaymentApplicationPageResp>(
    '/api/fina/payment-application/payment-application-drop-down-list',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 支付申请信息 GET /api/fina/payment-application/payment-application-info */
export async function paymentApplicationInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentApplicationInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultPaymentApplicationInfoResp>(
    '/api/fina/payment-application/payment-application-info',
    {
      method: 'GET',
      params: {
        ...params,
        req: undefined,
        ...params['req'],
      },
      ...(options || {}),
    },
  );
}

/** 支付申请分页 POST /api/fina/payment-application/payment-application-page */
export async function paymentApplicationPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPagePaymentApplicationPageResp>(
    '/api/fina/payment-application/payment-application-page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新支付申请 POST /api/fina/payment-application/payment-application-update */
export async function paymentApplicationUpdate(
  body: API.PaymentApplicationUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/fina/payment-application/payment-application-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
