// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 分配用户 POST /api/rules/assignUsers */
export async function assignUsers(body: API.NoticeUserReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/rules/assignUsers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 取消置顶 POST /api/rules/cancel-top */
export async function cancelTop(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/rules/cancel-top', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建制度相关信息 POST /api/rules/createRules */
export async function createRules(body: API.RulesReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/rules/createRules', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除制度相关信息 DELETE /api/rules/deleteRulesByIds */
export async function deleteRulesByIds(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/rules/deleteRulesByIds', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除用户查看制度信息 DELETE /api/rules/deleteUserViewInfo */
export async function deleteUserViewInfo(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/rules/deleteUserViewInfo', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新制度相关信息 PUT /api/rules/modifyRules */
export async function modifyRules(body: API.RulesReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/rules/modifyRules', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询制度相关信息 POST /api/rules/pageRules */
export async function pageRules(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageRulesResp>('/api/rules/pageRules', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 预览和修改用户意见表 POST /api/rules/preViewAndModify */
export async function preViewAndModify(
  body: API.UserViewRulesReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/rules/preViewAndModify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 主键查询制度相关信息 GET /api/rules/selectRulesById */
export async function selectRulesById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectRulesByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultRulesAndViewInfoResp>('/api/rules/selectRulesById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 置顶 POST /api/rules/top */
export async function top(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/rules/top', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
