// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 审批申请 POST /api/home/<USER>/activi-count */
export async function activiCount(options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/home/<USER>/activi-count', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 代办 POST /api/home/<USER>/agent */
export async function agent(options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/home/<USER>/agent', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 待审批列表 POST /api/home/<USER>/approval */
export async function approval(body: API.HomeWorkFlowReq, options?: { [key: string]: any }) {
  return request<API.ResultApprovalResp>('/api/home/<USER>/approval', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 待审批列表mobile POST /api/home/<USER>/approval-mobile */
export async function approvalMobile(body: API.MobileFlowReq, options?: { [key: string]: any }) {
  return request<API.ResultApprovalResp>('/api/home/<USER>/approval-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 设置常用功能 POST /api/home/<USER>/commonly-functions-update */
export async function commonlyFunctions(
  body: API.CommonlyFuncUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/home/<USER>/commonly-functions-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 休假申请 POST /api/home/<USER>/leave-app */
export async function leaveApplication(options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/home/<USER>/leave-app', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 代办列表 POST /api/home/<USER>/pending-dispose */
export async function pendingDispose(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/home/<USER>/pending-dispose', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 代办列表提醒 GET /api/home/<USER>/pending-tips */
export async function pendingTips(options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/home/<USER>/pending-tips', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 待提交列表 POST /api/home/<USER>/treat-submit */
export async function treatSubmit(body: API.HomeTreatSubmitReq, options?: { [key: string]: any }) {
  return request<API.ResultTreatSubmitResp>('/api/home/<USER>/treat-submit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
