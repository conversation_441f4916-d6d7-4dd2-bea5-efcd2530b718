// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 忘记密码 POST /api/forget-password */
export async function forgetPassword(body: API.EmailReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/forget-password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 登录 POST /api/login */
export async function login(body: API.AuthReq, options?: { [key: string]: any }) {
  return request<API.ResultAuthResp>('/api/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配至用户 POST /api/role/role-assignment-2-user */
export async function roleAssignment2User(
  body: API.RoleAssignment2UserReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/role/role-assignment-2-user', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建角色 POST /api/role/role-create */
export async function roleCreate(body: API.RoleInsertReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/role/role-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除角色 POST /api/role/role-del */
export async function roleDel(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/role/role-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 角色详情 POST /api/role/role-info */
export async function info(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultRoleInfoResp>('/api/role/role-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 角色分页 POST /api/role/role-page */
export async function rolePage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageRoleInfoResp>('/api/role/role-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新角色 POST /api/role/role-update */
export async function roleUpdate(body: API.RoleUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/role/role-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新角色状态 POST /api/role/role-update-status */
export async function roleUpdateStatus(
  body: API.RoleUpdateStatusReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/role/role-update-status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通讯录 POST /api/user/address-book */
export async function addressBook(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.addressBookParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListUserAddressBookResp>('/api/user/address-book', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通讯录 POST /api/user/address-book-body */
export async function addressBookBody(body: API.NameReq, options?: { [key: string]: any }) {
  return request<API.ResultListUserAddressBookResp>('/api/user/address-book-body', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取当前用户信息 GET /api/user/current-info */
export async function currentInfo(options?: { [key: string]: any }) {
  return request<API.ResultUserInfo>('/api/user/current-info', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 重置密码(超管 POST /api/user/reset-password */
export async function resetPassword(
  body: API.UserResetPasswordReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/user/reset-password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取下级 GET /api/user/subordinates-user */
export async function subordinatesUser(options?: { [key: string]: any }) {
  return request<API.ResultListUserDict>('/api/user/subordinates-user', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 禁用-激活(用户 POST /api/user/update-activation */
export async function updateActivation(
  body: API.UserUpdateActivationReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/user/update-activation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新档案信息 POST /api/user/update-archives */
export async function archives(body: API.UserArchivesReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/user/update-archives', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改密码(用户 POST /api/user/update-password */
export async function updatePassword(
  body: API.UserUpdatePasswordReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/user/update-password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建用户 POST /api/user/user-create */
export async function userCreate(body: API.UserInsertReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/user/user-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询部门用户列表 POST /api/user/user-department-list */
export async function userDepartmentList(
  body: API.DepartmentIdReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultListDict>('/api/user/user-department-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户下拉列表 POST /api/user/user-drop-down-list */
export async function userDropDownList(
  body: API.UserDropDownReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultListUserPageResp>('/api/user/user-drop-down-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户信息 GET /api/user/user-info */
export async function userInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultUserDetailResp>('/api/user/user-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 获取用户信息 GET /api/user/user-info-id/${param0} */
export async function userInfo1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userInfo1Params,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultUserDetailResp>(`/api/user/user-info-id/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 用户分页 POST /api/user/user-page */
export async function userPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageUserPageResp>('/api/user/user-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新用户 POST /api/user/user-update */
export async function updateUser(body: API.UserUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/user/user-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据部门id查询所属员工 POST /api/user/users-department-id */
export async function usersByDepartmentId(
  body: API.UsersByDepartmentIdReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultListUserPageResp>('/api/user/users-department-id', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id导出对应员工 POST /api/user/users-export */
export async function usersExport(body: string[], options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/user/users-export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通过表格导入用户基础信息 POST /api/user/users-import */
export async function importUser(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.importUserParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/user/users-import', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 微信小程序登录 POST /api/wx_login */
export async function wxLogin(body: API.WXAuthReq, options?: { [key: string]: any }) {
  return request<API.ResultAuthResp>('/api/wx_login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 微信小程序刷新登录 POST /api/wx_ref */
export async function wxRefToken(body: API.WXRefReq, options?: { [key: string]: any }) {
  return request<API.ResultAuthResp>('/api/wx_ref', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
