// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 根据项目ID关闭项目 POST /api/project/close-project */
export async function closeProject(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/close-project', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建售后项目 创建售后项目 POST /api/project/createAfterSalePro */
export async function createAfterSalePro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/createAfterSalePro', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建售前项目 创建售前项目 POST /api/project/createBeforeSalePro */
export async function createBeforeSalePro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/createBeforeSalePro', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建开发项目 创建开发项目 POST /api/project/createDevelopPro */
export async function createDevelopPro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/createDevelopPro', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建内部项目 创建内部项目 POST /api/project/createInnerPro */
export async function createInnerPro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/createInnerPro', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建销售项目 创建销售项目 POST /api/project/createSalePro */
export async function createSalePro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/createSalePro', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键删除项目 根据主键删除项目 DELETE /api/project/deleteProById */
export async function deleteProById(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/deleteProById', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改售后项目 修改售后项目 PUT /api/project/editAfterSalePro */
export async function editAfterSalePro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/editAfterSalePro', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改售前项目 修改售前项目 PUT /api/project/editBeforeSalePro */
export async function editBeforeSalePro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/editBeforeSalePro', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改开发项目 修改开发项目 PUT /api/project/editDevelopPro */
export async function editDevelopPro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/editDevelopPro', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改内部项目 修改内部项目 PUT /api/project/editInnerPro */
export async function editInnerPro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/editInnerPro', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改销售项目 修改销售项目 PUT /api/project/editSalePro */
export async function editSalePro(body: API.ProjectReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/editSalePro', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据客户id查询项目简要信息 根据客户id查询项目简要信息 POST /api/project/getBrieflyProInfo */
export async function getBrieflyProInfo(body: API.BrieflyProReq, options?: { [key: string]: any }) {
  return request<API.ResultListBrieflyProInfo>('/api/project/getBrieflyProInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询项目类型枚举值 查询项目类型枚举值 GET /api/project/getProTypeRespList */
export async function getProTypeRespList(options?: { [key: string]: any }) {
  return request<API.ResultListProjectTypeResp>('/api/project/getProTypeRespList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 移交项目 POST /api/project/handed-project */
export async function handedProject(body: API.ProHandedReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/project/handed-project', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键关闭项目 根据主键关闭项目 POST /api/project/open-and-close */
export async function openAndClose(
  body: API.ProjectOpenAndCloseReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/project/open-and-close', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询基础项目信息 分页查询基础项目信息 POST /api/project/pageSearchProject */
export async function pageSearchProject(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultProjectPageProBaseInfoResp>('/api/project/pageSearchProject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询可用项目（审核通过且状态正常） 查询可用项目（审核通过且状态正常） GET /api/project/projectList */
export async function projectList(options?: { [key: string]: any }) {
  return request<API.ResultListProBaseInfoResp>('/api/project/projectList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据项目基础信息主键查询该项目的详细信息 根据项目基础信息主键查询该项目的详细信息 GET /api/project/selectProjectById */
export async function selectProjectById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectProjectByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultProjectResp>('/api/project/selectProjectById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}
