// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 信息 POST /api/analysis/main-contract/info */
export async function mainContractAnalysisInfo(
  body: API.AnalysisInfoReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/main-contract/info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 列表 POST /api/analysis/main-contract/list */
export async function mainContractAnalysisList(
  body: API.AnalysisPageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/main-contract/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 售后项目-信息 POST /api/analysis/project/after-sales-info */
export async function afterSalesAnalysisInfo(
  body: API.AnalysisInfoReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/project/after-sales-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 售后项目-列表 POST /api/analysis/project/after-sales-list */
export async function afterSalesList(body: API.AnalysisPageReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/analysis/project/after-sales-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 内部项目-信息 POST /api/analysis/project/internal-info */
export async function internalAnalysisInfo(
  body: API.AnalysisInfoReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/project/internal-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 内部项目-列表 POST /api/analysis/project/internal-list */
export async function internalList(body: API.AnalysisPageReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/analysis/project/internal-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 售前项目-信息 POST /api/analysis/project/pre-sales-info */
export async function preSalesAnalysisInfo(
  body: API.AnalysisInfoReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/project/pre-sales-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 售前项目-列表 POST /api/analysis/project/pre-sales-list */
export async function preSalesList(body: API.AnalysisPageReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/analysis/project/pre-sales-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 销售项目-信息 POST /api/analysis/project/sales-info */
export async function salesAnalysisInfo(
  body: API.AnalysisInfoReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/project/sales-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 销售项目-列表 POST /api/analysis/project/sales-list */
export async function salesList(body: API.AnalysisPageReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/analysis/project/sales-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 采购付款计划表 POST /api/analysis/purchase-payment-plan/info */
export async function purchasePaymentPlanAnalysisInfo(
  body: API.PageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/purchase-payment-plan/info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 采购付款计划表 POST /api/analysis/purchase-payment-plan/list */
export async function purchasePaymentPlanAnalysisList(
  body: API.PageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/analysis/purchase-payment-plan/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 销售计划汇总表 POST /api/analysis/sales-plan/list */
export async function salesPlanAnalysisList(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/analysis/sales-plan/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 工作量统计表 POST /api/analysis/week/list */
export async function salesWeekAnalysisList(
  body: API.DatePageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultWeekAnalysisResp>('/api/analysis/week/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 收款计划信息表 POST /api/count/collection-plan/info */
export async function collectionPlanAnalysisInfo(
  body: API.PageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/count/collection-plan/info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 收款计划汇总表 POST /api/count/collection-plan/list */
export async function collectionPlanAnalysisList(
  body: API.PageReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/count/collection-plan/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
