// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 创建业务伙伴 创建业务伙伴 POST /api/partner/createPartner */
export async function createPartner(body: API.PartnerReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/partner/createPartner', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键删除业务伙伴 根据主键删除业务伙伴 DELETE /api/partner/deleteParById */
export async function deleteParById(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/partner/deleteParById', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询业务伙伴 分页查询业务伙伴 POST /api/partner/pagePartner */
export async function pagePartner(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPagePartnerInfoResp>('/api/partner/pagePartner', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键查询业务伙伴及其关联信息 根据主键查询业务伙伴及其关联信息 GET /api/partner/selectByPartnerId */
export async function selectByPartnerId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectByPartnerIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultPartnerResp>('/api/partner/selectByPartnerId', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 无条件查询业务伙伴 无条件查询业务伙伴 GET /api/partner/selectList */
export async function selectList(options?: { [key: string]: any }) {
  return request<API.ResultListPartnerInfoResp>('/api/partner/selectList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新业务伙伴 更新业务伙伴 PUT /api/partner/updatePartner */
export async function updatePartner(body: API.PartnerReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/partner/updatePartner', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
