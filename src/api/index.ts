// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as userSchedule from './userSchedule';
import * as train from './train';
import * as rules from './rules';
import * as project from './project';
import * as partner from './partner';
import * as contract from './contract';
import * as conInvoiced from './conInvoiced';
import * as certificate from './certificate';
import * as business from './business';
import * as auth from './auth';
import * as flow from './flow';
import * as workOrder from './workOrder';
import * as week from './week';
import * as task from './task';
import * as sysCertificate from './sysCertificate';
import * as sale from './sale';
import * as profit from './profit';
import * as nextcloud from './nextcloud';
import * as home from './home';
import * as fina from './fina';
import * as salary from './salary';
import * as paymentApplication from './paymentApplication';
import * as institutionCustomer from './institutionCustomer';
import * as talent from './talent';
import * as recruit from './recruit';
import * as position from './position';
import * as leaveType from './leaveType';
import * as leaveSell from './leaveSell';
import * as employee from './employee';
import * as leavePolicy from './leavePolicy';
import * as leaveApp from './leaveApp';
import * as holiday from './holiday';
import * as holidayDetails from './holidayDetails';
import * as grade from './grade';
import * as firmResource from './firmResource';
import * as employmentType from './employmentType';
import * as department from './department';
import * as attendance from './attendance';
import * as announcement from './announcement';
import * as analysis from './analysis';
import * as dictionary from './dictionary';
import * as collectTicket from './collectTicket';
import * as bid from './bid';
import * as scheduler from './scheduler';
import * as operationRecord from './operationRecord';
export default {
  userSchedule,
  train,
  rules,
  project,
  partner,
  contract,
  conInvoiced,
  certificate,
  business,
  auth,
  flow,
  workOrder,
  week,
  task,
  sysCertificate,
  sale,
  profit,
  nextcloud,
  home,
  fina,
  salary,
  paymentApplication,
  institutionCustomer,
  talent,
  recruit,
  position,
  leaveType,
  leaveSell,
  employee,
  leavePolicy,
  leaveApp,
  holiday,
  holidayDetails,
  grade,
  firmResource,
  employmentType,
  department,
  attendance,
  announcement,
  analysis,
  dictionary,
  collectTicket,
  bid,
  scheduler,
  operationRecord,
};
