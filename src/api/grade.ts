// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 创建员工级别 POST /api/employee/grade/grade-create */
export async function createGrade(
  body: API.EmployeeGradeInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/grade/grade-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除员工级别 POST /api/employee/grade/grade-del */
export async function gradeDel(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/grade/grade-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 员工级别信息 GET /api/employee/grade/grade-info */
export async function gradeInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.gradeInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultEmployeeGradeResp>('/api/employee/grade/grade-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 员工级别列表 GET /api/employee/grade/grade-list */
export async function gradeList(options?: { [key: string]: any }) {
  return request<API.ResultListEmployeeGradeListResp>('/api/employee/grade/grade-list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新员工级别 POST /api/employee/grade/grade-update */
export async function updateGrade(
  body: API.EmployeeGradeUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/grade/grade-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
