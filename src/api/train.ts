// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 创建培训报销 创建培训报销 POST /api/train/creatTrainRe */
export async function creatTrainRe(
  body: API.TrainReimbursementReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/train/creatTrainRe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除培训报销 删除培训报销 POST /api/train/deleteTrainIds */
export async function deleteTrainIds(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/train/deleteTrainIds', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页插叙培训报销 分页插叙培训报销 POST /api/train/pageSearchTrainRe */
export async function pageSearchTrainRe(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageTrainReimbursementResp>('/api/train/pageSearchTrainRe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 培训报销项目选择 GET /api/train/project */
export async function trainProjectList(options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/train/project', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据主键查询 根据主键查询 GET /api/train/selectTrainReById */
export async function selectTrainReById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.selectTrainReByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultTrainReimbursementResp>('/api/train/selectTrainReById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 更新培训报销 更新培训报销 PUT /api/train/updateTrainRe */
export async function updateTrainRe(
  body: API.TrainReimbursementReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/train/updateTrainRe', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新培训报销状态 更新培训报销状态 POST /api/train/updateTrainReStatus */
export async function updateTrainReStatus(
  body: API.TrainReimbursementStatusUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/train/updateTrainReStatus', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
