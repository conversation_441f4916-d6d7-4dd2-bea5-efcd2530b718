// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 创建员工职位 POST /api/employee/position/position-create */
export async function createPosition(
  body: API.PositionInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/position/position-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 员工职位删除 POST /api/employee/position/position-del */
export async function positionDel(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/position/position-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 员工职位列表 GET /api/employee/position/position-list */
export async function positionList(options?: { [key: string]: any }) {
  return request<API.ResultListPositionListResp>('/api/employee/position/position-list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新员工职位 POST /api/employee/position/position-update */
export async function updatePosition(
  body: API.PositionUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/position/position-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
