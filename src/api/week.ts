// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 周报导出 GET /api/week/week/attendance-export */
export async function attendanceCalendar1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.attendanceCalendar1Params,
  options?: { [key: string]: any },
) {
  return request<any>('/api/week/week/attendance-export', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 周报汇总人选项 POST /api/week/week/user-week-options */
export async function userWeekOptions(options?: { [key: string]: any }) {
  return request<API.ResultListDict>('/api/week/week/user-week-options', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 新建周报 POST /api/week/week/week-create */
export async function createWeek(body: API.WeekInsertReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/week/week/week-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 周报过期 GET /api/week/week/week-expired */
export async function expired(options?: { [key: string]: any }) {
  return request<any>('/api/week/week/week-expired', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 周报生成 GET /api/week/week/week-generate */
export async function weekGenerate(options?: { [key: string]: any }) {
  return request<any>('/api/week/week/week-generate', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 周报信息 GET /api/week/week/week-info */
export async function weekInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.weekInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultWeekInfoResp>('/api/week/week/week-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 周报分页 POST /api/week/week/week-page */
export async function weekPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageWeekPageResp>('/api/week/week/week-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新状态 POST /api/week/week/week-status */
export async function weekStatus(body: API.WeekStatusReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/week/week/week-status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新周报 POST /api/week/week/week-update */
export async function updateWeek(body: API.WeekUpdateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/week/week/week-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
