// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 查询审批详情 POST /api/work/flow/activi-info */
export async function getActiviInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultActiviInfoResp>('/api/work/flow/activi-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询审批详情(业务id) POST /api/work/flow/activi-info-business_key */
export async function getActiviInfoBusinessKey(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultActiviInfoResp>('/api/work/flow/activi-info-business_key', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询审批详情-移动 POST /api/work/flow/activi-info-mobile */
export async function getActiviInfoMobile(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultActiviInfoMobileResp>('/api/work/flow/activi-info-mobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同意 POST /api/work/flow/agree */
export async function agree(body: API.AgreeWorkFlowReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/work/flow/agree', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 定义流程 POST /api/work/flow/create */
export async function createWorkFlow(body: API.FlowEngineDTO, options?: { [key: string]: any }) {
  return request<any>('/api/work/flow/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 流程信息编辑 POST /api/work/flow/edit */
export async function workFlowEdit(
  body: API.RkWorkFlowUpdateDTO,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/work/flow/edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改审核中的售后项目 POST /api/work/flow/editAfterSalePro */
export async function editActivityAfterSalePro(
  body: API.ProjectReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/work/flow/editAfterSalePro', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 流程启用 POST /api/work/flow/enable */
export async function workFlowEnable(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/work/flow/enable', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 流程信息详情 POST /api/work/flow/info */
export async function workFlowInfo(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultRkWorkFlowPageResp>('/api/work/flow/info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** json转bpmn POST /api/work/flow/json-to-bpmn */
export async function jsonToBpmn(body: API.FlowEngineDTO, options?: { [key: string]: any }) {
  return request<any>('/api/work/flow/json-to-bpmn', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 流程信息列表 POST /api/work/flow/list */
export async function workFlowList(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageRkWorkFlowPageResp>('/api/work/flow/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 代办 POST /api/work/flow/pending-approval */
export async function pendingApproval(options?: { [key: string]: any }) {
  return request<API.ResultMapStringLong>('/api/work/flow/pending-approval', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 查询所有我审批的流程 POST /api/work/flow/process-instances */
export async function getProcessInstancesForUser(
  body: API.WorkFlowTypeReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultHistoricProcessInstancePageResp>('/api/work/flow/process-instances', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询流程序列 POST /api/work/flow/process-instances-rank */
export async function getProcessInstancesRank(
  body: API.WorkFlowRankReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultListProcessInstancesRankResp>('/api/work/flow/process-instances-rank', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 刷新流程数据 POST /api/work/flow/refresh-from-data */
export async function refreshFromData(
  body: API.RefreshFromDataReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/work/flow/refresh-from-data', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 拒绝 POST /api/work/flow/refuse */
export async function refuse(body: API.RefuseWorkFlowReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/work/flow/refuse', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 退回 POST /api/work/flow/reject */
export async function reject(body: API.RefuseWorkFlowReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/work/flow/reject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除流程 POST /api/work/flow/remove */
export async function workFlowRemove(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/work/flow/remove', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 定义流程 POST /api/work/flow/rk-create */
export async function createRkWorkFlow(
  body: API.RkWorkFlowCreateDTO,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/work/flow/rk-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 启动流程 POST /api/work/flow/start */
export async function startWorkFlow(body: API.StartWorkFlowReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/work/flow/start', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
