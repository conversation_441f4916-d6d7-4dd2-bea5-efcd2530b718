// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 申请销假 POST /api/employee/leave-sell/leave-app-create */
export async function createLeaveSell(
  body: API.LeaveSellInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-sell/leave-app-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除销假 POST /api/employee/leave-sell/leave-app-del */
export async function delLeaveSell(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/leave-sell/leave-app-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 销假信息 GET /api/employee/leave-sell/leave-app-info */
export async function leaveSellInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.leaveSellInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultLeaveSellInfoResp>('/api/employee/leave-sell/leave-app-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 销假分页 POST /api/employee/leave-sell/leave-app-page */
export async function leaveSellPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageLeaveSellInfoResp>('/api/employee/leave-sell/leave-app-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新销假 POST /api/employee/leave-sell/leave-app-update */
export async function updateLeaveSell(
  body: API.LeaveSellUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-sell/leave-app-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
