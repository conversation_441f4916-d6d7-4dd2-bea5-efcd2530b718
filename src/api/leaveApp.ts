// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 休假剩余天数列表 GET /api/employee/leave-app/leave-app-balance */
export async function leaveAppBalance(options?: { [key: string]: any }) {
  return request<API.ResultListLeaveBalanceListResp>('/api/employee/leave-app/leave-app-balance', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 申请休假 POST /api/employee/leave-app/leave-app-create */
export async function createLeaveApp(
  body: API.LeaveAppInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-app/leave-app-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除休假 POST /api/employee/leave-app/leave-app-del */
export async function delLeaveApp(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/employee/leave-app/leave-app-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 休假信息 GET /api/employee/leave-app/leave-app-info */
export async function leaveAppInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.leaveAppInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultLeaveAppInfoResp>('/api/employee/leave-app/leave-app-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 休假分页 POST /api/employee/leave-app/leave-app-page */
export async function leaveAppPage(body: API.LeavePageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageLeaveAppPageResp>('/api/employee/leave-app/leave-app-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新休假 POST /api/employee/leave-app/leave-app-update */
export async function updateLeaveApp(
  body: API.LeaveAppUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/employee/leave-app/leave-app-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
