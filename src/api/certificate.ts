// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 创建证书 创建证书 POST /api/certificate/createCer */
export async function createCer(body: API.CertificateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/certificate/createCer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除 批量删除 DELETE /api/certificate/deleteCerByIds */
export async function deleteCerByIds(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/certificate/deleteCerByIds', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询 分页查询 POST /api/certificate/pageCer */
export async function pageCer(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageCertificateResp>('/api/certificate/pageCer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 主键查询 主键查询 POST /api/certificate/selectCerById */
export async function selectCerById(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultCertificateResp>('/api/certificate/selectCerById', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新对象 更新对象 PUT /api/certificate/updateCer */
export async function updateCer(body: API.CertificateReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/certificate/updateCer', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
