// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 创建证书类型 POST /api/sys-certificate/createCertificate */
export async function createCertificate(
  body: API.SysCertificateInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sys-certificate/createCertificate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除证书类型 POST /api/sys-certificate/delCertificate */
export async function delCertificate(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/sys-certificate/delCertificate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 证书类型列表 POST /api/sys-certificate/listCertificate */
export async function listCertificate(options?: { [key: string]: any }) {
  return request<API.ResultListSysCertificateListResp>('/api/sys-certificate/listCertificate', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 更新证书类型 POST /api/sys-certificate/updateCertificate */
export async function updateCertificate(
  body: API.SysCertificateUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/sys-certificate/updateCertificate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
