import { useGlobalIconFont } from './components/IconFont/helper';

export default defineAppConfig({
  pages: ['pages/index/index'],
  lazyCodeLoading: 'requiredComponents',
  subPackages: [
    {
      root: 'pages/approvals',
      pages: ['index', 'components/ApprovalDetail/index'],
    },
    {
      root: 'pages/hr',
      pages: [
        'LeaveApplication/index',
        'BillingInformation/index',
        'LeaveSell/index',
        'Announcement/index',
        'AddressBook/index',
      ],
    },
    {
      root: 'pages/contract',
      pages: ['ApplyInvoice/index', 'ApplyPayment/index'],
    },
    {
      root: 'pages/finance',
      pages: [
        'GeneralPayment/index',
        'Receipt/index',
        'Claim/index',
        'PaymentStatement/index',
        'OutStandingPayment/index',
      ],
    },
    {
      root: 'pages/weekly',
      pages: ['index', 'components/WeeklyForm/index'],
    },
    {
      root: 'pages/workorder',
      pages: ['index', 'components/Detail/index'],
    },
    {
      root: 'pages/ai',
      pages: ['index'],
    },
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
  },
  usingComponents: { ...useGlobalIconFont() },
});
