Component({
  properties: {
    // more | xinjianbuhegedanbeifen | a-bianzu32beifen2 | anbaoxunjianbeifen | financial | apply_payment | flow | approval | pending | tendency | dianbiaochaobiaojilubeifen | a-fangyuanguanlibeifen2 | a-fangyuanjiansuobeifen2 | a-huodongbaomingbeifen2 | a-fangyuantongjibeifen2 | workorder | leave | fangyuanliebiaobeifen | kanfangguanlibeifen | statistics1 | claimed | a-kejichanyefuwubeifen2 | statistics | a-huiyishiyudingbeifen32x | all_applications | author | collection | a-liuyantousubeifen22x | shebeixunjianbeifen | payment | wuyebaoxiu | approved | payment_list | schedule | a-shebeiguzhangbaobiaobeifen2 | weekly | submitted | a-wofaqidebeifen2 | zonghenenghaotongjifenxibeifen | customer | yuanqucanyinbeifen | zhuanjieguanlibeifen | a-shebeixinxibeifen2 | shebeiguanlibeifen | weibaojilubeifen | my_approval | claim | a-shebeiweixiushangbaobeifen2 | tousujianyibeifen | wuyezixunbeifen | leave_sell | pm
    name: {
      type: String,
    },
    // string | string[]
    color: {
      type: null,
      observer: function (color) {
        this.setData({
          colors: this.fixColor(),
          isStr: typeof color === 'string',
        });
      },
    },
    size: {
      type: Number,
      value: 18,
      observer: function (size) {
        this.setData({
          svgSize: (size / 750) * wx.getSystemInfoSync().windowWidth,
        });
      },
    },
  },
  data: {
    colors: '',
    svgSize: (18 / 750) * wx.getSystemInfoSync().windowWidth,
    quot: '"',
    isStr: true,
  },
  methods: {
    fixColor: function () {
      var color = this.data.color;
      var hex2rgb = this.hex2rgb;

      if (typeof color === 'string') {
        return color.indexOf('#') === 0 ? hex2rgb(color) : color;
      }

      return color.map(function (item) {
        return item.indexOf('#') === 0 ? hex2rgb(item) : item;
      });
    },
    hex2rgb: function (hex) {
      var rgb = [];

      hex = hex.substr(1);

      if (hex.length === 3) {
        hex = hex.replace(/(.)/g, '$1$1');
      }

      hex.replace(/../g, function (color) {
        rgb.push(parseInt(color, 0x10));
        return color;
      });

      return 'rgb(' + rgb.join(',') + ')';
    },
  },
});
