/* tslint:disable */
/* eslint-disable */

import React, { FunctionComponent } from 'react';

export type IconNames =
  | 'more'
  | 'xinjianbuhegedanbeifen'
  | 'a-bianzu32beifen2'
  | 'anbaoxunjianbeifen'
  | 'financial'
  | 'apply_payment'
  | 'flow'
  | 'approval'
  | 'pending'
  | 'tendency'
  | 'dianbiaochaobiaojilubeifen'
  | 'a-fangyuanguanlibeifen2'
  | 'a-fangyuanjiansuobeifen2'
  | 'a-huodongbaomingbeifen2'
  | 'a-fangyuantongjibeifen2'
  | 'workorder'
  | 'leave'
  | 'fangyuanliebiaobeifen'
  | 'kanfangguanlibeifen'
  | 'statistics1'
  | 'claimed'
  | 'a-kejichanyefuwubeifen2'
  | 'statistics'
  | 'a-huiyishiyudingbeifen32x'
  | 'all_applications'
  | 'author'
  | 'collection'
  | 'a-liuyantousubeifen22x'
  | 'shebeixunjianbeifen'
  | 'payment'
  | 'wuyebaoxiu'
  | 'approved'
  | 'payment_list'
  | 'schedule'
  | 'a-shebeiguzhangbaobiaobeifen2'
  | 'weekly'
  | 'submitted'
  | 'a-wofaqidebeifen2'
  | 'zonghenenghaotongjifenxibeifen'
  | 'customer'
  | 'yuanqucanyinbeifen'
  | 'zhuanjieguanlibeifen'
  | 'a-shebeixinxibeifen2'
  | 'shebeiguanlibeifen'
  | 'weibaojilubeifen'
  | 'my_approval'
  | 'claim'
  | 'a-shebeiweixiushangbaobeifen2'
  | 'tousujianyibeifen'
  | 'wuyezixunbeifen'
  | 'leave_sell'
  | 'pm';

export interface IconProps {
  name: IconNames;
  size?: number;
  color?: string | string[];
  style?: React.CSSProperties;
}

const IconFont: FunctionComponent<IconProps> = () => {
  return null;
};

export default IconFont;
