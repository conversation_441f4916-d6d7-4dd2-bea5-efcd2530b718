.multi-select {
  position: relative;
  padding-top: 50px;
  background-color: #fff;
  // Checkbox 容器样式，添加滚动条
  &-checkbox-container {
    height: 300px;
    padding: 20px 0; // 添加内边距
  }

  &-confirm {
    position: absolute;
    top: 6px;
    right: 0px;
  }

  &-cancel {
    white-space: nowrap;
  }

  &-search {
    position: relative;
    display: flex;
    align-items: center;
    margin: 0 20px;

    &_input {
      width: 90%;
      height: 30px;
      margin-right: 10px;
      padding-left: 10px;
      background: #f6f7f9;
    }

    &_clear {
      position: absolute;
      right: 40px;
      z-index: 99;
    }

    &_submit {
      flex: 1;
    }
  }

  // Checkbox 样式
  .nut-checkbox {
    --nutui-checkbox-label-font-size: 14px;

    margin: 8px 20px;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .nut-icon {
      width: 16px;
      height: 16px;
    }
  }
}
