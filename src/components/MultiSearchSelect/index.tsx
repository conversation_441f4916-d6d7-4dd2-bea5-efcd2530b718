import React, { useEffect, useRef, useState } from 'react';
import { MaskClose, Search } from '@nutui/icons-react-taro';
import { Button, Checkbox, CheckboxGroup, Popup } from '@nutui/nutui-react-taro';
import { Input, ScrollView } from '@tarojs/components';

import './index.scss';

export interface MultiSelectItemProps {
  text: string;
  value: string;
}

interface MultiSelectProps {
  originList: MultiSelectItemProps[];
  visible: boolean;
  close: () => void;
  done: (options: MultiSelectItemProps[]) => void;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  originList = [],
  visible = false,
  close,
  done,
}) => {
  const [searchList, setSearchList] = useState<MultiSelectItemProps[]>([]);
  const [selected, setSelected] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const inputRef = useRef<{ value: string }>(null);

  useEffect(() => {
    setSearchList([...originList]);
  }, [originList]);

  const handleSearch = (value: string) => {
    const filteredList = originList.filter((item) => item.text.includes(value));
    setSearchList(filteredList);
  };

  const handleClear = () => {
    if (inputRef.current) {
      inputRef.current.value = '';
      setSearchValue('');
      setSearchList([...originList]);
      setSelected([]);
    }
  };

  return (
    <Popup
      closeable
      closeIconPosition='top-left'
      closeIcon={<div className='multi-select-cancel'>取消</div>}
      visible={visible}
      position='bottom'
      onClose={close}
    >
      <div className='multi-select'>
        <Button
          className='multi-select-confirm'
          fill='none'
          type='primary'
          onClick={() => {
            const selectedItems = searchList.filter((item) => selected.includes(item.value));
            done(selectedItems);
            close();
          }}
        >
          确认
        </Button>
        <div className='multi-select-search'>
          <Input
            ref={inputRef}
            className='multi-select-search_input'
            type='text'
            confirmType='search'
            placeholder='请输入查询关键字'
            onInput={(val) => {
              const value = val.detail.value;
              setSearchValue(value);
              handleSearch(value);
            }}
          />
          <MaskClose
            className='multi-select-search_clear'
            color='#8A8A8A'
            size={20}
            onClick={handleClear}
          />
          <Search
            className='multi-select-search_submit'
            color='#8A8A8A'
            size={20}
            onClick={() => handleSearch(searchValue)}
          />
        </div>
        <ScrollView scrollY className='multi-select-checkbox-container'>
          <CheckboxGroup
            value={selected}
            onChange={(values) => {
              setSelected(values as string[]);
            }}
          >
            {searchList.map((item) => (
              <Checkbox key={item.value} value={item.value}>
                {item.text}
              </Checkbox>
            ))}
          </CheckboxGroup>
        </ScrollView>
      </div>
    </Popup>
  );
};

export default MultiSelect;
