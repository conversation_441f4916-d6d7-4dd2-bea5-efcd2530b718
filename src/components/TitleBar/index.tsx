import React from 'react';
import { ArrowLeft } from '@nutui/icons-react-taro';
import { NavBar } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

import { useSafeStatusbar } from '@/hooks';

import './index.scss';

interface TitleBarProps {
  title: string | undefined;
  onNavBack?: () => void;
}

const TitleBar: React.FC<TitleBarProps> = ({ title, onNavBack }) => {
  const navHeight = useSafeStatusbar();

  const handleBackClick = () => {
    try {
      Taro.navigateBack();
    } catch (error) {
      console.error('导航失败:', error);
    }
  };

  return (
    <NavBar
      back={
        <View className='title-bar_back'>
          <ArrowLeft size={16} />
          <View>返回</View>
        </View>
      }
      titleAlign='center'
      fixed
      placeholder
      onBackClick={onNavBack || handleBackClick}
      className='title-bar'
      style={{ height: 44 + navHeight }}
    >
      {title}
    </NavBar>
  );
};

export default TitleBar;
