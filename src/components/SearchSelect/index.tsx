import React, { useEffect, useRef, useState } from 'react';
import { MaskClose, Search } from '@nutui/icons-react-taro';
import { Picker } from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { Input } from '@tarojs/components';

import './index.scss';

export interface SearchItemProps extends PickerOption {
  text: string;
  value: string;
}

interface SearchSelectProps {
  originList: SearchItemProps[];
  visible: boolean;
  close: () => void;
  done: (options: SearchItemProps[]) => void;
}


const SearchSelect: React.FC<SearchSelectProps> = ({ originList = [], visible = false, close, done }) => {

  const [searchList, setSearchList] = useState<SearchItemProps[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const inputRef = useRef<{ value: string }>(null);

  useEffect(() => {
    setSearchList([...originList]);
  }, []);

  return (
    <Picker
      className='select'
      visible={visible}
      options={searchList}
      onClose={() => {
        close();
      }}
      onConfirm={(options: SearchItemProps[]) => {
        done(options);
      }}
    >
      <div className='select-search'>
        <Input
          ref={inputRef}
          className='select-search_input'
          type='text'
          confirmType='search'
          placeholder='请输入查询关键字'
          onConfirm={(val) => {
            const arr = originList.filter((item) => {
              return item?.text?.includes(val.detail.value);
            });
            setSearchList(arr);
          }}
          onInput={(val) => {
            setSearchValue(val.detail.value);
          }}
        />
        <MaskClose
          className='select-search_clear'
          color='#8A8A8A'
          size={20}
          onClick={() => {
            if (inputRef.current) {
              inputRef.current.value = '';
              setSearchValue('');
              setSearchList([...originList]);
            }
          }}
        />
        <Search
          className='select-search_submit'
          color='#8A8A8A'
          size={20}
          onClick={() => {
            const arr = originList.filter((item) => {
              return item?.text?.includes(searchValue);
            });
            setSearchList(arr);
          }}
        />
      </div>
    </Picker>
  );
};

export default SearchSelect;
