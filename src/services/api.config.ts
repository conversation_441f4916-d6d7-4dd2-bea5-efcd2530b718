let baseUrlPrefix = '';
const env = process.env.NODE_ENV === 'development' ? 'development' : 'production';
console.log('编译环境：', process.env.NODE_ENV);
switch (env) {
  case 'development':
    // baseUrlPrefix = 'https://rk-oa.mynatapp.cc';
    // baseUrlPrefix = 'https://oam.rklink.cn';
    baseUrlPrefix = 'http://192.168.100.102:31999';
    // baseUrlPrefix = 'https://rkzl-oa-test.ddnsto.com';
    // baseUrlPrefix = 'http://172.17.0.48:18080';
    break;
  case 'production':
    baseUrlPrefix = 'https://oam.rklink.cn';
    break;
}

const api = {
  baseUrl: baseUrlPrefix,
};

export default api;
