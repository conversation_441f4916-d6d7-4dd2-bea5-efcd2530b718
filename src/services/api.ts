import Taro from '@tarojs/taro';

import apiConfig from './api.config';

//网络请求拦截器
const interceptor = function (chain) {
  const requestParams = chain.requestParams;
  // const { method, data, url } = requestParams;
  let token = Taro.getStorageSync('RKLINK_OA_TOKEN'); //拿到本地缓存中存的token
  if (requestParams.url.includes('https://ark.cn-beijing.volces.com')) {
    requestParams.header = {
      ...requestParams.header,
      Authorization: requestParams.header.Authorization,
    };
  } else {
    requestParams.header = {
      ...requestParams.header,
      Authorization: token, //将token添加到头部
    };
  }
  return chain.proceed(requestParams).then((res) => {
    return res;
  });
};

Taro.addInterceptor(interceptor);

const request = async <T = any>(url: string, config: Record<string, any>): Promise<T> => {
  const { method, params } = config;
  let contentType =
    method === 'POST' || method === 'PUT'
      ? 'application/json'
      : 'application/x-www-form-urlencoded';
  const option = {
    isShowLoading: false,
    url: apiConfig.baseUrl + url,
    header: {
      'content-type': contentType,
    },
    data: params,
    success(res: any) {
      // 状态码401为后台未获取到用户的手机号
      if (res?.statusCode !== 200 || res?.data?.code !== 200) {
        if (res?.data?.code === 403) {
          Taro.showToast({
            title: '重新登录中',
            icon: 'loading',
          });
          Taro.eventCenter.trigger('INTERNAL_EXPIRATION');
        } else if (res?.data?.code === 401) {
          Taro.showToast({
            title: '请绑定手机号',
            icon: 'error',
          });
          Taro.eventCenter.trigger('NEED_PHONE');
        } else {
          Taro.showToast({ title: res?.data?.message || '请求失败!', icon: 'error' });
        }
      }
    },
    error(e) {
      console.log('api', '请求接口出现问题', e);
      Taro.showToast({ title: '请求失败!', icon: 'error' });
    },
    ...config,
  };

  const resp = await Taro.request(option);
  return resp.data;
};

export default request;
