import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { useRequest } from 'ahooks';

import { pagePartner } from '@/api/partner';

//note: 仅下拉框使用,筛选出审核通过的业务伙伴
export function usePartnerList() {
  const { data, loading } = useRequest(() =>
    pagePartner({
      pageNum: 1,
      pageSize: 1000,
      search: { activiStatus: '2' },
    }),
  );
  return {
    partnerList:
      data?.data?.records?.map(
        (i) =>
          ({
            ...i,
            text: i.clientName,
            value: i.id,
          } as PickerOption & API.PartnerInfoResp),
      ) || [],
    loading,
  };
}
