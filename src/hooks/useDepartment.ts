import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { useRequest } from 'ahooks';

import { getDepartmentList } from '@/api/department';

export function useDepartment() {
  const { data, loading } = useRequest(getDepartmentList);
  return {
    departmentList:
      data?.data?.map(
        (i) =>
          ({
            ...i,
            value: i.id,
            text: i.departmentName,
          } as PickerOption),
      ) || [],
    loading,
  };
}
