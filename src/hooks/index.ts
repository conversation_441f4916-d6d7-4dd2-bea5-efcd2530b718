import { useEffect, useState } from 'react';
import Taro from '@tarojs/taro';

export function useSafeStatusbar() {
  const [navHeight, setNavHeight] = useState<number>(0);

  useEffect(() => {
    const windowInfo = Taro.getWindowInfo();
    const statusBarHeight = windowInfo?.safeArea?.top || windowInfo.statusBarHeight || 0;
    setNavHeight(statusBarHeight);
  }, []);

  return navHeight;
}

export function useDebounceCallback(callback: (...args: any[]) => void, wait: number) {
  const [timerId, setTimerId] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [timerId]);

  const debounceWrapper = (...args: any[]) => {
    clearTimeout(timerId!);
    setTimerId(setTimeout(() => callback(...args), wait));
  };

  return debounceWrapper;
}

// 节流 Hook
export function useThrottleCallback(callback: (...args: any[]) => void, wait: number) {
  let lastCalled = 0;
  let timerId: NodeJS.Timeout | null = null;

  const throttledWrapper = (...args: any[]) => {
    const now = Date.now();
    const remainingTime = wait - (now - lastCalled);

    if (remainingTime <= 0 || remainingTime > wait) {
      clearTimeout(timerId!);
      lastCalled = now;
      callback(...args);
    } else if (!timerId) {
      timerId = setTimeout(() => {
        lastCalled = Date.now();
        callback(...args);
      }, remainingTime);
    }
  };

  useEffect(() => {
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, []);

  return throttledWrapper;
}
