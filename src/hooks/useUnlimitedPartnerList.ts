import { useRequest } from 'ahooks';

import { selectList } from '@/api/partner';

export function useUnlimitedPartnerList() {
  const { data, loading } = useRequest(() => selectList());

  return {
    //note: 目前仅收款记录使用,无权限限制,收款记录中对方单位仅为客户和招标机构,招标机构和客户不可能重复
    partnerList:
      data?.data?.map((item: API.PartnerInfoResp) => ({
        ...item,
        text: item.clientName,
        value: item.id,
      })) || [],
    loading,
  };
}
