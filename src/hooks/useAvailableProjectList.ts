// note: 可用项目，目前仅报销、周报、工单、项目付款申请所有下拉筛出可用项目，项目成员、项目经理、创建人可见

import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { useRequest } from 'ahooks';

import { projectList } from '@/api/project';

export function useAvailableProjectList() {
  const { data, loading } = useRequest(() => projectList());
  return {
    availableProjectList:
      data?.data?.map(
        (i) =>
          ({
            ...i,
            value: i.projectNumber,
            text: `${i.projectNumber}(${i.projectName})`,
          } as PickerOption),
      ) || [],
    loading,
  };
}
