import { create } from 'zustand';

interface LoginState {
  userInfo: API.UserInfo | null;
  needPhone: boolean;
  setUserInfo: (userInfo: API.UserInfo | null) => void;
  setNeedPhone: (needPhone: boolean) => void;
  setApprovalCount: (approvalCount: number) => void;
  setApprovalData: (approvalData: any) => void;
  approvalCount: number;
  approvalData: any;
}

export const useLoginStore = create<LoginState>((set) => ({
  //用户信息
  userInfo: null,
  //用户尚未绑定手机号
  needPhone: false,
  //待审批总数
  approvalCount: 0,
  //待审批数据
  approvalData: {},
  setUserInfo: (userInfo) => {
    set({ userInfo });
  },
  setNeedPhone: (needPhone) => {
    set({ needPhone });
  },
  setApprovalCount: (approvalCount) => {
    set({ approvalCount });
  },
  setApprovalData: (approvalData) => {
    set({ approvalData });
  },
}));
