import Taro from '@tarojs/taro';

/**
 * 计算想要的元素可用高度
 * @param tabBarHeight 底部标签栏高度，默认为0
 * @param titleHeight 标题栏高度，若未提供，则默认为小程序自带的导航栏高度
 * @returns 返回可用高度；
 */
export const calcAvailableHeight = (tabBarHeight = 0, titleHeight?: number): number => {
  const titleBarHeight = titleHeight ? titleHeight : Taro.getMenuButtonBoundingClientRect().height;

  const windowInfo = Taro.getWindowInfo();

  if (windowInfo?.safeArea?.height && windowInfo?.safeArea?.top) {
    return windowInfo?.safeArea?.height - titleBarHeight - tabBarHeight;
  }
  const statusBarHeight = windowInfo?.statusBarHeight || 0;
  return (
    (windowInfo?.safeArea?.height || windowInfo.windowHeight) -
    statusBarHeight -
    titleBarHeight -
    tabBarHeight
  );
};

/**
 * 计算可用的屏幕底部空间
 * 该函数没有参数。
 * @returns {number | boolean} 返回可用底部空间的高度（单位：px）；如果无法获取可用底部空间，则返回false。
 */
export const calcAvailableBottom = (): number | boolean => {
  const sysInfo = Taro.getSystemInfoSync();
  if (sysInfo?.safeArea?.bottom) {
    const windowHeight = sysInfo.windowHeight;
    return windowHeight - sysInfo.safeArea.bottom;
  }
  return false;
};

/**
 * 根据字符编码生成渐变颜色对象
 * @param charCode 字符编码，用于计算颜色的渐变效果
 * @returns 返回一个对象，包含背景颜色、背景图片线性渐变设置、背景大小、背景重复和边框样式属性
 */
export const getGradientColorFromCharCode = (charCode: number) => {
  if (Number.isNaN(charCode)) return {};
  const hue = charCode % 360;
  const colorArray = [
    ['#21D4FD', '#B721FF'],
    ['#4158D0', '#C850C0'],
    ['#5BDED1', '#DC32B9'],
    ['#36A415', '#1F65A6'],
    ['#FBDA61', '#FF5ACD'],
    ['#3EECAC', '#EE74E1'],
  ];
  // 根据 charCode 计算索引
  const combinationIndex = charCode % colorArray.length;

  // 获取对应索引的颜色组合
  const randomCombination = colorArray[combinationIndex];
  const [color1, color2] = randomCombination;
  return {
    backgroundColor: color1,
    backgroundImage: `linear-gradient(${hue}deg, ${color1} 0%, ${color2} 100%)`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    border: 'none',
  };
};

export const calcDayBetweenToday = (dateString = '2000-01-01') => {
  // 将字符串转换为Date对象
  const inputDate = new Date(dateString);

  // 获取当前日期
  const currentDate = new Date();

  // 计算两个日期之间的差值（以毫秒为单位）
  const differenceInMilliseconds = currentDate.getTime() - inputDate.getTime();

  // 转换为天数（向下取整）
  let differenceInDays = Math.floor(differenceInMilliseconds / (1000 * 60 * 60 * 24));
  differenceInDays = differenceInDays < 0 ? 0 : differenceInDays;

  return differenceInDays === 0 ? '今天发起' : `${differenceInDays}天前发起`;
};

/**
 * 判断给定值是否为非空
 * 对于数字类型，0 被视为非空值
 * @param value 需要判断的任意类型值
 * @returns {boolean} 如果值为非空则返回true，否则返回false
 */
export const isValueNotEmpty = (value: any): boolean => {
  if (typeof value === 'number' && value === 0) {
    // 特殊处理数字0，将其视为非空值
    return true;
  } else if (value !== null && value !== undefined && value !== '') {
    // 其他情况下，如果值存在且不为空，则认为是非空值
    return true;
  }
  // 否则，判断为为空值
  return false;
};
/**
 * 将枚举值转换为对象
 * @param arr
 * @returns {key:value}
 */
export const getObject = (arr: { label: string; value: string }[]) => {
  return arr.reduce((previousValue, current) => {
    return { ...previousValue, [current.value]: current.label };
  }, {});
};

/**
 *  计算税率
 * @param amount 金额
 * @param rate 税率
 * @returns taxInclusiveAmount 税额  taxExclusiveAmount 不含税金额
 */
export const calcTaxRate = (amount: number, rate: number) => {
  if (!amount || !rate)
    return {
      taxInclusiveAmount: 0,
      taxExclusiveAmount: 0,
    };

  // 不含税金额 = 含税金额 / (1+税率）
  const taxExclusiveAmount = Number((amount / (1 + rate / 100)).toFixed(2));

  return {
    // 税额 = 不含税金额 * 税率
    taxInclusiveAmount: (taxExclusiveAmount * (rate / 100)).toFixed(2),
    taxExclusiveAmount,
  };
};

/**
 *  获取随机id
 */
export const getRandomId = () => {
  return (Math.random() * 1000000).toFixed(0);
};
