import * as TextEncoding from 'text-encoding-shim';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}
export interface ChatResponse {
  content: string;
}

// 添加解析流式数据的函数
function parseStreamData(data: string) {
  try {
    // 处理多条数据的情况
    const chunks = data
      .split('\n')
      .filter((line) => line.trim() !== '')
      .filter((line) => line.startsWith('data: '))
      .map((line) => line.replace('data: ', ''))
      .filter((line) => line !== '[DONE]'); // 过滤结束标记

    let content = '';
    for (const chunk of chunks) {
      try {
        const json = JSON.parse(chunk);
        const delta = json?.choices?.[0]?.delta?.content;
        if (delta) {
          content += delta;
        }
      } catch (e) {
        console.log('Skipping invalid JSON chunk:', chunk);
        continue;
      }
    }
    return content;
  } catch (error) {
    console.error('Parse stream data error:', error);
    return '';
  }
}

export async function chatCompletion(
  messages: ChatMessage[],
  type: string,
  onmessage: (str: string) => void,
) {
  try {
    // @ts-ignore
    const response = wx.request({
      url: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      method: 'POST',
      enableChunked: true,
      data: {
        model: type === 'v3' ? 'deepseek-v3-241226' : 'deepseek-r1-250120',
        messages,
        max_tokens: 2000,
        stream: true,
      },
      header: {
        'Content-Type': 'application/json',
        Authorization: `Bearer 36f62f7b-6077-436e-b855-1686535915e4`,
      },
    });
    response.onChunkReceived((res) => {
      const str = new TextEncoding.TextDecoder('utf-8').decode(new Uint8Array(res.data));
      const content = parseStreamData(str);
      if (content) {
        onmessage(content);
      }
    });
  } catch (error) {
    console.error('AI API Error:', error);
    throw error;
  }
}
