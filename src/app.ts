import { PropsWithChildren, useMemo } from 'react';
import Taro, { useDidHide, useDidShow, useLaunch } from '@tarojs/taro';
import { useRequest } from 'ahooks';

import { useDebounceCallback } from '@/hooks';
import { useLoginStore } from '@/store';

import { currentInfo, wxLogin, wxRefToken } from './api/auth';
import { pendingApproval } from './api/flow';

import './app.scss';

// eslint-disable-next-line import/first
import 'dayjs/locale/zh-cn';

function App({ children }: PropsWithChildren<any>) {
  const updateManager = Taro.getUpdateManager();
  updateManager.onUpdateReady(() => {
    Taro.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用',
      confirmText: '确定',
      cancelText: '取消',
      success: function (res) {
        if (res.confirm) {
          updateManager.applyUpdate();
        }
      },
    });
  });
  updateManager.onUpdateFailed(() => {
    Taro.showToast({
      title: '新版本下载失败',
    });
  });

  const store = useLoginStore();
  const showApproval = useMemo(
    () => store.userInfo?.pagePermissions?.includes('APPROVAL'),
    [store.userInfo],
  );
  useRequest(() => pendingApproval(), {
    ready: !!store?.userInfo && showApproval,
    pollingInterval: 5000,
    onSuccess: (res) => {
      const approvalCount =
        Object.values(res?.data || {})?.reduce((accumulator: number, currentValue: number) => {
          return accumulator + currentValue;
        }, 0) || 0;
      const approvalData = res?.data || {};
      store.setApprovalCount(approvalCount);
      store.setApprovalData(approvalData);
    },
  });

  // 内部token过期，重新登录逻辑
  const handleRefresh = async () => {
    console.log('内部TOKEN已过期，正在重新登录');
    Taro.removeStorageSync('RKLINK_OA_TOKEN');
    store.setUserInfo(null);
    const refreshToken = Taro.getStorageSync('RKLINK_OA_REFRESH_TOKEN');
    //如果没有刷新TOKEN，则重新走微信登录
    if (refreshToken) {
      Taro.removeStorageSync('RKLINK_OA_REFRESH_TOKEN');
      const res = await wxRefToken({ refreshAuthorization: refreshToken });
      await handleLogRes(res);
    } else {
      handleLogin();
    }
  };
  const debouncedHandleRefresh = useDebounceCallback(handleRefresh, 2000);

  const setNeedPhone = () => {
    store.setNeedPhone(true);
  };

  useLaunch(async () => {
    // 判断登录是否失效
    let token = Taro.getStorageSync('RKLINK_OA_TOKEN');
    if (!token) {
      handleLogin();
    } else {
      const userInfo = await currentInfo({});
      if (userInfo?.data) {
        store.setUserInfo(userInfo.data);
      }
    }
  });

  useDidShow(() => {
    console.log('页面加载');
    Taro.eventCenter.on('INTERNAL_EXPIRATION', debouncedHandleRefresh);
    Taro.eventCenter.on('NEED_PHONE', setNeedPhone);
  });

  useDidHide(() => {
    console.log('页面隐藏');
    Taro.eventCenter.off('INTERNAL_EXPIRATION');
    Taro.eventCenter.off('NEED_PHONE');
  });

  // 微信登录逻辑
  const handleLogin = () => {
    Taro.login({
      success: async (res) => {
        console.warn('Taro.login方法获取的回调', res);
        if (res.code) {
          // 发送 res.code 到后端换取 openId, sessionKey 等数据
          const loginRes = await wxLogin({ code: res.code });
          console.warn('wx-login接口获取的回调', loginRes);
          if (loginRes.code === 200) {
            await handleLogRes(loginRes);
          }
        } else {
          console.error('登录失败！', res.errMsg);
        }
      },
      fail: (err) => {
        console.error('登录接口调用失败', err);
      },
    });
  };

  // 处理wxLogin或wxRefToken接口的返回逻辑
  const handleLogRes = async (res) => {
    if (res?.data?.authorization) {
      Taro.setStorageSync('RKLINK_OA_TOKEN', res.data.authorization);
      Taro.setStorageSync('RKLINK_OA_REFRESH_TOKEN', res.data.refreshAuthorization);
      const userInfo = await currentInfo({});
      if (userInfo?.data) {
        store.setUserInfo(userInfo.data);
        Taro.showToast({ title: '登录成功', icon: 'success' });
      }
    }
  };

  // children 是将要会渲染的页面
  return children;
}

export default App;
