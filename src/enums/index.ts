export const approvalList = [
  {
    title: '全部',
    key: 'ALL',
  },
  {
    title: '休假审批',
    key: 'LEAVE_APPROVAL_PROCESS',
  },
  {
    title: '销假审批',
    key: 'REVOCATION_APPROVAL',
  },
  {
    title: '报销审批',
    key: 'REIMBURSEMENT_APPROVAL',
  },
  {
    title: '培训报销审批',
    key: 'TRAINING_REIMBURSEMENT_APPROVAL',
  },
  {
    title: '工单审批',
    key: 'WORK_ORDER_APPROVAL',
  },
  {
    title: '售后项目审批',
    key: 'PROJECT_APPROVAL_SH',
  },
  {
    title: '开发项目审批',
    key: 'PROJECT_APPROVAL_KF',
  },
  {
    title: '售前项目审批',
    key: 'PROJECT_APPROVAL_SQ',
  },
  {
    title: '销售项目审批',
    key: 'PROJECT_APPROVAL_XS',
  },
  {
    title: '内部项目审批',
    key: 'PROJECT_APPROVAL_NB',
  },
  {
    title: '业务伙伴审核',
    key: 'BUSINESS_PARTNER',
  },
  {
    title: '项目付款审批',
    key: 'UNIVERSAL_PAYMENT',
  },
  {
    title: '合同付款审批',
    key: 'CONTRACT_PROCUREMENT_PAYMENT_APPLICATION',
  },
  {
    title: '资源使用审批',
    key: 'RESOURCE_APPLICATION',
  },
  {
    title: '招聘申请审批',
    key: 'RECRUITMENT_APPLICATION',
  },
  {
    title: '主合同审批',
    key: 'MAIN_CONTRACT_APPROVAL',
  },
  {
    title: '采购合同审批',
    key: 'PURCHASE_CONTRACT_APPROVAL',
  },
  {
    title: '内部下单合同审批',
    key: 'INTERNAL_CONTRACT_APPROVAL',
  },
  {
    title: '公告审批',
    key: 'ANNOUNCEMENT_APPROVAL',
  },
  {
    title: '投标项目报备',
    key: 'BID_FILING_APPROVAL',
  },
];

// 审批列表-下部tabbar三种类型
export const ApprovalStatus = [
  { title: '待办', key: 'AGENT' },
  { title: '已办', key: 'DONE' },
  { title: '已发起', key: 'INITIATED' },
];

//审批列表-审批状态
export const ActiviStatus = [
  { title: '待提交', key: '0', background: '#E9E9E9' },
  { title: '审批中', key: '1', background: '#52dbc7' },
  { title: '通过', key: '2', background: '#52C319' },
  { title: '拒绝', key: '3', background: '#FA4D4F' },
  { title: '驳回', key: '4', background: '#FA4D4F' },
];

//项目类型
export const ProjectTypes = [
  { title: '内部项目', key: 'NB' },
  { title: '售前项目', key: 'SQ' },
  { title: '售后项目', key: 'SH' },
  { title: '销售项目', key: 'XS' },
];

//工单类型
export const WORK_ORDER_TYPE = [
  { value: 'CUSTOMER_ONSITE_SERVICE', label: '客户现场服务' },
  { value: 'CUSTOMER_ONLINE_SERVICE', label: '客户联机服务' },
  { value: 'CUSTOMER_CONSULTATION_SERVICE', label: '客户咨询服务' },
  { value: 'ENGINEER_WORK_RECORD', label: '工程师工作记录' },
  { value: 'SECOND_LINE_EXPERT_SUPPORT', label: '二线专家支持' },
];

// 合作方式
export const COOPERATION_WAYS = [
  { value: 'SELF_OP', label: '自营' },
  { value: 'COOP', label: '合作' },
];

// 投标方式  现场/线上
export const BIDDING_TYPE = [
  {
    label: '现场',
    value: 'LOCAL',
  },
  {
    label: '线上',
    value: 'ONLINE',
  },
];
