{
  "compilerOptions": {
    // 跳过第三方库的类型检查
    "skipLibCheck": true,
    "esModuleInterop": true,
    "target": "es2017",
    "module": "commonjs",
    "removeComments": false,
    "preserveConstEnums": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "allowSyntheticDefaultImports": true,
    "outDir": "lib",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictNullChecks": true,
    "sourceMap": true,
    "rootDir": ".",
    "jsx": "react-jsx",
    "allowJs": true,
    "resolveJsonModule": true,
    "paths": {
      "@/*": [
        "./src/*"
      ]
    }
  },
  "include": [
    "./src",
    "./types",
    "./config"
  ],
  "exclude": [
    "node_modules" // 明确排除 node_modules 目录
  ],
  "compileOnSave": false
}