{"miniprogramRoot": "dist/", "description": "oa mobile", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": true, "minified": true, "ignoreUploadUnusedFiles": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true}, "compileType": "miniprogram", "srcMiniprogramRoot": "dist/", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.10.1", "packOptions": {"ignore": [], "include": []}, "appid": "wxde06897839d012e2", "projectname": "oa-mobile"}