{"name": "oa-mobile", "version": "1.0.0", "private": true, "description": "oa mobile", "author": "", "scripts": {"build:alipay": "taro build --type alipay", "build:h5": "taro build --type h5", "build:harmony-hybrid": "taro build --type harmony-hybrid", "build:jd": "taro build --type jd", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "build:rn": "taro build --type rn", "build:swan": "taro build --type swan", "build:tt": "taro build --type tt", "build:weapp": "taro build --type weapp", "dev:alipay": "npm run build:alipay -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:weapp": "npm run build:weapp -- --watch", "generator-icon": "npx iconfont-taro", "lint:prettier": "prettier -c --write \"src/**/*.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "ts-node openapi.config.ts", "prepare": "husky", "prettier": "prettier -c --write \"src/**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "dependencies": {"@babel/runtime": "^7.21.5", "@nutui/nutui-react-taro": "^2.6.9", "@tarojs/components": "3.6.24", "@tarojs/helper": "3.6.24", "@tarojs/plugin-framework-react": "3.6.24", "@tarojs/plugin-html": "^3.6.24", "@tarojs/plugin-platform-alipay": "3.6.24", "@tarojs/plugin-platform-h5": "3.6.24", "@tarojs/plugin-platform-harmony-hybrid": "3.6.24", "@tarojs/plugin-platform-jd": "3.6.24", "@tarojs/plugin-platform-qq": "3.6.24", "@tarojs/plugin-platform-swan": "3.6.24", "@tarojs/plugin-platform-tt": "3.6.24", "@tarojs/plugin-platform-weapp": "3.6.24", "@tarojs/react": "3.6.24", "@tarojs/runtime": "3.6.24", "@tarojs/shared": "3.6.24", "@tarojs/taro": "3.6.24", "ahooks": "^3.7.10", "babel-plugin-import": "^1.13.8", "dayjs": "^1.11.10", "react": "^18.0.0", "react-dom": "^18.0.0", "text-encoding-shim": "^1.0.5", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.8.0", "@commitlint/cli": "^19.2.0", "@commitlint/config-conventional": "^19.1.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.24", "@tarojs/taro-loader": "3.6.24", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.24", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@umijs/openapi": "^1.11.1", "babel-preset-taro": "3.6.24", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-config-taro": "3.6.24", "eslint-plugin-import": "^2.12.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-simple-import-sort": "^12.0.0", "husky": "^9.0.11", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "lint-staged": "^15.2.2", "postcss": "^8.4.18", "prettier": "^2.7.1", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "taro-iconfont-cli": "^3.3.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.1.0", "webpack": "5.78.0"}, "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}}