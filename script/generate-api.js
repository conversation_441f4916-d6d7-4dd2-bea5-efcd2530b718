// @ts-ignore
/* eslint-disable */
const axios = require('axios');
const fs = require('fs').promises; // 使用fs.promises以支持异步写入文件
const path = require('path');
const os = require('os');

// const apiUrl = 'https://rk-oa.mynatapp.cc';
const apiUrl = 'http://192.168.100.102:31999';

const { exec } = require('child_process');

// 获取当前脚本所在目录
const rootDirectory = path.resolve(__dirname, '..');
const openApiCommandPath = path.join(rootDirectory, 'node_modules/.bin/openapi');

// 计算openapi.json和要输出的src/api目录相对于当前脚本的路径
const openApiInput = path.join(rootDirectory, 'openapi.json');
const openApiOutput = path.join(rootDirectory, 'src/api');

// 切换到包含openapi命令的目录

process.chdir(rootDirectory);

// 定义要执行的OpenAPI命令
const openApiCommand = `${openApiCommandPath}  --input ${apiUrl} --output ${openApiOutput} --client fetch --exportSchemas false --exportServices false`;

// 执行命令并处理输出和错误
exec(openApiCommand, (error, stdout, stderr) => {
  if (error) {
    console.error(`执行OpenAPI命令时出错: ${error}`);
    return;
  }
  console.log(`OpenAPI命令执行成功，标准输出：\n${stdout}`);
  // 调用fetchData函数
  fetchData();
  if (stderr) {
    console.warn(`OpenAPI命令执行时有警告信息：\n${stderr}`);
  }
});

// 一个用于获取数据并处理的异步函数
async function fetchData() {
  try {
    const response = await axios.get(apiUrl);

    // 进行数据验证，确保返回的数据是预期的格式
    if (
      !response.data ||
      typeof response.data !== 'object' ||
      typeof response.data.paths !== 'object'
    ) {
      throw new Error('Invalid data format received from API');
    }

    const paths = response.data.paths;
    const generatedFunctions = generateRequestFunctions(paths);

    // 使用异步方式写入文件
    const fileName = '../src/api/http.ts';
    const filePath = path.join(__dirname, fileName);

    await fs.writeFile(filePath, generatedFunctions, { flag: 'w' });
    console.log(`Generated functions saved to ${path.relative(os.homedir(), filePath)}`);
  } catch (error) {
    console.error('Error:', error.message || error);
  }
}

// 优化后的生成函数代码
function generateRequestFunctions(paths) {
  let requestFunctions = [];

  Object.entries(paths).forEach(([path, methods]) => {
    Object.entries(methods).forEach(([method, operation]) => {
      const summary = operation.summary;
      const operationId = operation.operationId;
      const responseType =
        operation.responses['200'] &&
        operation.responses['200'].content &&
        operation.responses['200'].content['*/*'] &&
        operation.responses['200'].content['*/*'].schema &&
        operation.responses?.['200']?.content?.['*/*'].schema?.['$ref'].split('/').pop();
      const requestBodyType = operation.requestBody?.content['application/json']?.schema['$ref']
        .split('/')
        .pop();

      // 使用模板字符串来提高代码的可读性
      const escapedPath = interpolatePathVariable(path);
      const funcStr = `
/**
 * ${summary}
 * @param data 请求的参数
 * @param options 可选的对象，用于扩展或覆盖默认请求选项
 * @returns 返回一个Promise，解析为任意类型的响应数据
*/
export async function ${operationId}(
  data: ${requestBodyType ? `API.${requestBodyType}` : 'any'},
  options?: { [key: string]: any },
): Promise<${responseType ? `API.${responseType}` : 'any'}> {
  return request<${responseType ? `API.${responseType}` : 'any'}>(\`${escapedPath}\`, {
    method: '${method.toUpperCase()}',
    data,
    ...(options || {}),
  });
}`;

      requestFunctions.push(funcStr);
    });
  });

  // 在生成的文件顶部添加引入语句
  const importStatement = `// @ts-ignore\n/* eslint-disable */\n\nimport request from '@/services/api';\nimport * as API from './index';\n\n`;

  return importStatement + requestFunctions.join('\n\n');
}

// 一个用于替换路径参数的辅助函数
function interpolatePathVariable(path) {
  // 使用正则表达式查找路径中的变量，并用${data.propertyName}形式替换
  const variablePattern = /{(\w+)}/g;

  return path.replace(variablePattern, (_, propertyName) => {
    return `\$\{data.${propertyName}\}`;
  });
}

// 一个用于转义路径参数的辅助函数，以防止潜在的注入攻击
function escapePath(path) {
  // 注意：这里只是一个简单的示例实现，具体实现应根据实际情况进行调整，以确保安全性
  return path.replace(/'/g, '\\\'');
}
